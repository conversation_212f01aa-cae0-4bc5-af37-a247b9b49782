# 月卷积发货额统计函数代码逻辑详细分析

## 函数概述

`f_dm_foc_mid_month_item_dms` 是一个PostgreSQL存储函数，主要功能是**分视角统计ITEM的月卷积发货额**。该函数支持三种产业类型（ICT、数字能源、IAS），三种维度类型（通用、盈利、量纲颗粒度），两种业务口径（发货成本、收入时点）。

## 核心业务逻辑

### 1. 参数体系

#### 输入参数
- `f_industry_flag`：产业类型标识
  - `'I'`：ICT产业
  - `'E'`：数字能源产业  
  - `'IAS'`：IAS产业
- `f_caliber_flag`：业务口径标识
  - `'C'`：发货成本时点
  - `'R'`：收入确认时点
- `f_dimension_type`：维度类型标识
  - `'U'`：通用颗粒度（Universal）
  - `'P'`：盈利颗粒度（Profit）
  - `'D'`：量纲颗粒度（Dimension）
- `f_period_id`：统计期间ID
- `f_keystr`：数据解密密钥

#### 输出参数
- `x_result_status`：执行结果状态（'1'成功，'0'失败）

### 2. 表映射逻辑

函数根据产业类型和业务口径的组合，动态确定数据源表和目标表：

#### 源表映射规则
```
ICT + 发货成本(C) → DM_FOC_BOM_ITEM_SHIP_DTL_T + DM_FOC_DATA_PRIMARY_ENCRYPT_T
ICT + 收入时点(R) → DM_FOC_REVENUE_ITEM_SHIP_DTL_T + DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T
数字能源 + 发货成本(C) → DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T + DM_FOC_DATA_PRIMARY_ENCRYPT_T
数字能源 + 收入时点(R) → DM_FOC_ENERGY_REVENUE_ITEM_SHIP_DTL_T + DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T
IAS + 发货成本(C) → DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T + DM_FOC_DATA_PRIMARY_ENCRYPT_T
IAS + 收入时点(R) → DM_FOC_IAS_REVENUE_ITEM_SHIP_DTL_T + DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T
```

#### 目标表映射规则
```
ICT + 通用颗粒度 + 发货成本 → DM_FOC_MID_MONTH_ITEM_TMP_CU
ICT + 盈利颗粒度 + 发货成本 → DM_FOC_MID_MONTH_ITEM_TMP_CP
ICT + 量纲颗粒度 + 发货成本 → DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS
ICT + 通用颗粒度 + 收入时点 → DM_FOC_MID_MONTH_ITEM_TMP_RU
ICT + 盈利颗粒度 + 收入时点 → DM_FOC_MID_MONTH_ITEM_TMP_RP
ICT + 量纲颗粒度 + 收入时点 → DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS
（数字能源和IAS类似，表名前缀分别为ENERGY和IAS）
```

### 3. 视角配置逻辑

不同的维度类型和产业类型组合对应不同的视角范围：

#### 视角范围配置
```
通用颗粒度：
- ICT/数字能源：视角0-3
- IAS：视角0-4

盈利颗粒度：
- 所有产业：视角3-4

量纲颗粒度：
- ICT：视角0-11
- 数字能源：视角0-12
- IAS：视角0-12
```

### 4. 数据处理流程

#### 第一阶段：基础数据准备
1. **创建临时表**：`BASE_DATA_TEMP`，包含所有必要的业务字段
2. **数据提取和解密**：
   - 从源表提取原始数据
   - 通过加密表进行数据解密
   - 根据业务口径计算成本金额：
     - 发货成本(C)：直接使用解密后的成本金额
     - 收入时点(R)：使用均价×数量计算
3. **数据扩展**：
   - 插入原始数据（包含国内D和海外O标识）
   - 生成全球数据（OVERSEA_FLAG='G'）

#### 第二阶段：视角数据处理
1. **创建视角临时表**：`VIEW_DATA_TEMP`，增加视角标识字段
2. **循环处理各视角**：
   - 根据视角编号和业务参数确定聚合维度
   - 动态配置包含/排除的字段
   - 执行数据聚合和分组
   - 生成集团汇总数据

#### 第三阶段：结果输出
1. **数据插入**：将处理后的视角数据插入目标表
2. **统计信息收集**：执行ANALYZE更新表统计信息

### 5. 字段动态配置逻辑

根据维度类型和产业类型的不同，动态包含或排除特定字段：

#### 通用颗粒度(U)
- **排除字段**：L1_NAME, L2_NAME, 所有量纲字段, SPART字段, COA字段
- **ICT/数字能源额外排除**：LV4相关字段
- **IAS保留**：LV4相关字段

#### 盈利颗粒度(P)  
- **排除字段**：LV3字段, 所有量纲字段, SPART字段, COA字段
- **所有产业都排除**：LV4相关字段

#### 量纲颗粒度(D)
- **排除字段**：L1_NAME, L2_NAME
- **ICT特殊处理**：排除COA字段和LV4字段
- **数字能源特殊处理**：保留COA字段，排除LV4字段  
- **IAS特殊处理**：排除COA字段，保留LV4字段

### 6. 视角映射复杂逻辑

原函数中最复杂的部分是视角编号的映射逻辑，不同的维度类型和产业类型组合会将循环中的视角编号映射到实际的业务视角：

#### 示例映射规则
```
通用颗粒度 + ICT/数字能源：
- 循环视角3 → 实际视角0
- 循环视角2 → 实际视角1  
- 循环视角1 → 实际视角2
- 循环视角0 → 实际视角3

量纲颗粒度 + ICT：
- 循环视角11 → 实际视角0（视角1）
- 循环视角10 → 实际视角1（视角2）
- ...
- 循环视角0 → 实际视角11
```

### 7. 数据过滤和业务规则

#### 数据质量过滤
- 只处理金额和数量都大于0的记录
- 排除特定的LV1重量级团队：'101764','100005','135741','104237','133341'

#### 量纲数据特殊处理
- 量纲颗粒度要求DIMENSION_CODE不为空
- 不同产业类型对量纲子类字段有不同的包含规则

#### 集团数据生成
- 每个视角都会额外生成一份BG='集团'的汇总数据
- 集团数据通过对当前视角数据的再次聚合生成

## 重构改进点

### 1. 配置外部化
将硬编码的表名、字段名、业务规则抽离到配置文件中，提高可维护性。

### 2. 逻辑模块化  
将复杂的条件判断逻辑拆分为专门的配置函数，减少主函数的复杂度。

### 3. 日志增强
在关键步骤增加详细的日志记录，包含具体的参数值和处理结果。

### 4. 错误处理优化
增加参数验证和异常处理，提供更明确的错误信息。

### 5. 性能优化
通过减少动态SQL构建和优化临时表结构提升执行效率。

## 总结

这个函数是一个典型的企业级数据处理函数，具有以下特点：
- **业务复杂度高**：支持多种产业、维度、口径的组合
- **数据量大**：处理月度的ITEM级别发货数据
- **逻辑复杂**：包含复杂的视角映射和字段配置逻辑
- **可扩展性要求**：需要支持新产业类型的快速接入

重构的目标是在保持功能完整性的前提下，提高代码的可读性、可维护性和可扩展性。
