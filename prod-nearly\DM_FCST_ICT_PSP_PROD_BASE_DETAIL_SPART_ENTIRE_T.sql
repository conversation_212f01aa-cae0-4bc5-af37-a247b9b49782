-- DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_ENTIRE_T
-- 
INSERT INTO FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_ENTIRE_T 
                (VERSION_ID,
				 PERIOD_ID ,
				 PERIOD_YEAR ,
				 R<PERSON><PERSON>_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				 LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 RMB_AVG_AMT ,
				 SPART_CODE,SPART_CN_NAME ,
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				  SOFTWARE_MARK,
				 VIEW_FLAG ,
				 APPEND_FLAG,
                 CREATED_BY,
                 CREATION_DATE,
                 LAST_UPDATED_BY,
                 LAST_UPDATE_DATE,
                 DEL_FLAG
                )
                
    WITH FORWARD_FILLER_TEMP AS
     (
      --按照重量级团队, 采购信息维补齐, 前向补齐均价
      SELECT  PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				 LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 RMB_AVG_AMT ,
				 FIRST_VALUE(T.RMB_AVG_AMT) OVER(PARTITION BY LV0_PROD_LIST_CODE,LV1_PROD_LIST_CODE,LV2_PROD_LIST_CODE,LV3_PROD_LIST_CODE,LV4_PROD_LIST_CODE,SPART_CODE, SOFTWARE_MARK,REGION_CODE,REPOFFICE_CODE,BG_CODE ,OVERSEA_FLAG,MAIN_FLAG,CODE_ATTRIBUTES,VIEW_FLAG,AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
				 SPART_CODE,
				 APPEND_FLAG,
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				  SOFTWARE_MARK,
				 VIEW_FLAG,
				 AVG_AMT_FLAG
        FROM (SELECT  
                      PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				 LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 RMB_AVG_AMT ,
				 SPART_CODE, 
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				 SUM(A.NULL_FLAG) OVER(PARTITION BY LV0_PROD_LIST_CODE,LV1_PROD_LIST_CODE,LV2_PROD_LIST_CODE,LV3_PROD_LIST_CODE,LV4_PROD_LIST_CODE,SPART_CODE, SOFTWARE_MARK,REGION_CODE,REPOFFICE_CODE,BG_CODE ,OVERSEA_FLAG,MAIN_FLAG,CODE_ATTRIBUTES,VIEW_FLAG ORDER BY PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
				  SOFTWARE_MARK,
				 VIEW_FLAG,
				 APPEND_FLAG
                 FROM ACTUAL_PSP_APD_TEMP A
				 ) T)
    
    --向后补齐均价
    SELECT 		 46,
				 PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				 A.LV0_PROD_LIST_CODE,A.LV0_PROD_LIST_CN_NAME,	A.LV1_PROD_LIST_CODE,A.LV1_PROD_LIST_CN_NAME, A.LV2_PROD_LIST_CODE,A.LV2_PROD_LIST_CN_NAME, A.LV3_PROD_LIST_CODE,A.LV3_PROD_LIST_CN_NAME, A.LV4_PROD_LIST_CODE,A.LV4_PROD_LIST_CN_NAME,
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 (NVL(A.AVG_AMT_2, A.AVG_AMT_3)) AS RMB_AVG_AMT,-- 202403版本新逻辑，不解密直接补齐 ,
				 SPART_CODE,SPART_CODE AS SPART_CN_NAME ,
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				  SOFTWARE_MARK,
				 VIEW_FLAG ,
				  CASE WHEN SPART_CODE = 'SNULL' THEN 'N' ELSE APPEND_FLAG 
	END AS APPEND_FLAG, 
                 -1 AS CREATED_BY,
				CURRENT_TIMESTAMP AS CREATION_DATE,
				-1 AS LAST_UPDATED_BY,
				CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
				'N' AS DEL_FLAG
				
      FROM (SELECT  A.PERIOD_ID ,
				 A.PERIOD_YEAR ,
				 A.REGION_CODE ,
				 A.REGION_CN_NAME ,
				 A.REPOFFICE_CODE ,
				 A.REPOFFICE_CN_NAME ,
				 A.BG_CODE ,
				 A.BG_CN_NAME ,
				 A.LV0_PROD_LIST_CODE,A.LV0_PROD_LIST_CN_NAME,	A.LV1_PROD_LIST_CODE,A.LV1_PROD_LIST_CN_NAME, A.LV2_PROD_LIST_CODE,A.LV2_PROD_LIST_CN_NAME, A.LV3_PROD_LIST_CODE,A.LV3_PROD_LIST_CN_NAME, A.LV4_PROD_LIST_CODE,A.LV4_PROD_LIST_CN_NAME,
				 A.PROD_QTY ,
				 A.RMB_COST_AMT ,
				 A.SPART_CODE,
				 A.OVERSEA_FLAG,
				 A.MAIN_FLAG,
				 A.CODE_ATTRIBUTES,
				  A.SOFTWARE_MARK,
				 A.VIEW_FLAG ,
				 APPEND_FLAG,
                 A.AVG_AMT_2,
                 B.AVG_AMT_3
              FROM FORWARD_FILLER_TEMP A
              LEFT JOIN (SELECT DISTINCT A.REGION_CODE,
                                        A.REPOFFICE_CODE,
                                        A.BG_CODE,
                                         A.SPART_CODE,
                                        A.OVERSEA_FLAG,
                                        A.MAIN_FLAG,
										A.CODE_ATTRIBUTES,
										 A.SOFTWARE_MARK,
										A.VIEW_FLAG,
                                       A.LV0_PROD_LIST_CODE,A.LV1_PROD_LIST_CODE,A.LV2_PROD_LIST_CODE,A.LV3_PROD_LIST_CODE,A.LV4_PROD_LIST_CODE,
                                        FIRST_VALUE(A.PERIOD_ID) OVER(PARTITION BY A.BG_CODE,A.VIEW_FLAG,A.OVERSEA_FLAG,A.MAIN_FLAG,A.CODE_ATTRIBUTES,A.LV0_PROD_LIST_CODE,A.LV1_PROD_LIST_CODE,A.LV2_PROD_LIST_CODE,A.LV3_PROD_LIST_CODE,A.LV4_PROD_LIST_CODE,A.SPART_CODE, A.SOFTWARE_MARK,A.REGION_CODE,A.REPOFFICE_CODE ORDER BY A.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(A.AVG_AMT_2) OVER(PARTITION BY A.BG_CODE,A.VIEW_FLAG,A.OVERSEA_FLAG,A.MAIN_FLAG,A.CODE_ATTRIBUTES,A.LV0_PROD_LIST_CODE,A.LV1_PROD_LIST_CODE,A.LV2_PROD_LIST_CODE,A.LV3_PROD_LIST_CODE,A.LV4_PROD_LIST_CODE,A.SPART_CODE, A.SOFTWARE_MARK,A.REGION_CODE,A.REPOFFICE_CODE ORDER BY A.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP A
                         WHERE A.AVG_AMT_FLAG > 0) B
                ON A.VIEW_FLAG = B.VIEW_FLAG   
               AND A.REGION_CODE = B.REGION_CODE
               AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
               AND A.BG_CODE = B.BG_CODE
               AND A.PERIOD_ID < B.PERIOD_ID
               AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
               AND A.MAIN_FLAG = B.MAIN_FLAG
               AND NVL(A.CODE_ATTRIBUTES,1) = NVL(B.CODE_ATTRIBUTES,1)
			   AND NVL(A.SOFTWARE_MARK,2) = NVL(B.SOFTWARE_MARK,2)
			    AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE   AND A.SPART_CODE = B.SPART_CODE  ) A