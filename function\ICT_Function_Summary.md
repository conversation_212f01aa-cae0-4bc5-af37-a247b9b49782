# ICT产业专用函数说明文档

## 函数名称
`f_dm_foc_ict_item_dtl_inner`

## 功能概述
从原始的通用函数 `f_dm_foc_item_dtl_inner` 中单独拆分出ICT产业的专用逻辑，实现常量内联化，专门处理ICT产业的实际发货明细数据。

## 主要改进点

### 1. 函数签名简化
- **原函数**: `f_dm_foc_item_dtl_inner(f_industry_flag, f_year, OUT x_result_status)`
- **新函数**: `f_dm_foc_ict_item_dtl_inner(f_year, OUT x_result_status)`
- 移除了 `f_industry_flag` 参数，因为专门处理ICT产业

### 2. 常量内联化
将原函数中的动态变量替换为ICT产业的固定常量：

#### 目标表常量
- **固定目标表**: `FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T`

#### 业务常量
- **LV0_PROD_RND_TEAM_CODE**: `'104364'` (IRB - ICT产业固定代码)
- **版本信息表**: `FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T`
- **序列**: `FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S`

#### 字段映射常量
- **PROD_CODE字段**: ICT产业特有，包含在INSERT和SELECT中
- **CEG_TEMP**: 使用ICT专用的品类-专家团映射逻辑

### 3. 简化的逻辑结构
- 移除了产业类型判断的IF-ELSIF-ELSE逻辑
- 直接使用ICT产业的固定配置
- 简化了版本管理逻辑，专门针对ICT版本表

### 4. 优化的SQL结构
```sql
-- ICT专用的CEG_TEMP定义
WITH CEG_TEMP AS (
  SELECT DISTINCT CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
    L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME
  FROM (
    -- 合并ENERGY和ICT的品类映射，优先ICT
    SELECT ... FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_CATG_CEG_ICT_D ...
    UNION ALL
    SELECT ... FROM FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D ...
  )
  WHERE RANK = 1
)
```

### 5. 专用字段处理
- **PROD_CODE**: ICT产业特有的产品编码字段
- **LV0_PROD_RND_TEAM_CODE**: 固定为'104364' (IRB)
- **版本管理**: 专门使用ICT版本信息表

## 使用方式

### 函数调用
```sql
SELECT FIN_DM_OPT_FOI.f_dm_foc_ict_item_dtl_inner('202412');
```

### 参数说明
- `f_year`: 处理的年月，格式如'202412'
- `x_result_status`: 输出参数，'1'表示成功，'0'表示失败

## 性能优势
1. **减少条件判断**: 移除了产业类型的动态判断逻辑
2. **常量优化**: 数据库可以更好地优化包含常量的查询
3. **专用索引**: 可以针对ICT产业的特定查询模式建立专用索引
4. **简化维护**: 专门的函数更容易维护和调试

## 数据流程
1. **清理数据**: 删除指定年月的ICT历史数据
2. **版本管理**: 检查或创建ICT版本信息
3. **数据关联**: 关联多个维度表获取完整的ICT产业数据
4. **数据插入**: 将处理后的数据插入ICT专用表
5. **统计信息**: 更新表的统计信息以优化查询性能

## 关键表结构
- **主表**: `FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T`
- **版本表**: `FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T`
- **维度表**: 包括物料维度、产品维度、专家团映射等

## 注意事项
1. 该函数专门处理ICT产业数据，不适用于其他产业
2. 需要确保相关维度表数据的完整性
3. 建议在数据量较大时考虑分批处理
4. 定期检查版本信息表的数据一致性
