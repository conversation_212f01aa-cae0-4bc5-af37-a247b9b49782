# SQL子查询优化总结

## 优化概述
将 `f_dm_foc_month_cost_idx_dms_ict_dimension.sql` 函数中的所有子查询重构为临时表方式，以提升查询性能和可维护性。

## 优化内容

### 1. 第6层：SUB_DETAIL -> SUBCATEGORY 量纲子类卷积
**原始代码：**
```sql
FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUB_DETAIL') T1
JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUB_DETAIL') T2
```

**优化后：**
```sql
-- 创建临时表：SUB_DETAIL层级基础指数数据
CREATE TEMP TABLE temp_sub_detail_base_idx AS
SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS 
WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUB_DETAIL';

-- 为临时表创建联合索引（基于JOIN条件）
CREATE INDEX idx_temp_sub_detail_base ON temp_sub_detail_base_idx 
(GROUP_CODE, VIEW_FLAG, PARENT_CODE, CALIBER_FLAG);

-- 创建临时表：SUB_DETAIL层级权重数据
CREATE TEMP TABLE temp_sub_detail_weight AS
SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T 
WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUB_DETAIL';

-- 为临时表创建联合索引（基于JOIN条件）
CREATE INDEX idx_temp_sub_detail_weight ON temp_sub_detail_weight 
(GROUP_CODE, VIEW_FLAG, PARENT_CODE, CALIBER_FLAG);

-- 使用临时表进行JOIN
FROM temp_sub_detail_base_idx T1
JOIN temp_sub_detail_weight T2
 ON T1.GROUP_CODE = T2.GROUP_CODE AND T1.VIEW_FLAG = T2.VIEW_FLAG 
    AND T1.PARENT_CODE = T2.PARENT_CODE AND T1.CALIBER_FLAG = T2.CALIBER_FLAG

-- 清理临时表
DROP TABLE temp_sub_detail_base_idx;
DROP TABLE temp_sub_detail_weight;
```

### 2. 第7层：SUBCATEGORY -> DIMENSION 量纲卷积
**JOIN条件索引：** `(GROUP_CODE, VIEW_FLAG, PARENT_CODE, CALIBER_FLAG)`

### 3. 第8层：DIMENSION -> LV1/LV2/LV3 分视角卷积
**JOIN条件索引：** `(GROUP_CODE, VIEW_FLAG, PARENT_CODE, CALIBER_FLAG)`

### 4. 第9-12层：重量级团队层级卷积
**原始代码：** 使用FOR循环和CASE语句
**优化后：** 展开为独立的语句块，每层都使用临时表
- 第9层：LV4 -> LV3，JOIN条件索引：`(GROUP_CODE)`
- 第10层：LV3 -> LV2，JOIN条件索引：`(GROUP_CODE)`
- 第11层：LV2 -> LV1，JOIN条件索引：`(GROUP_CODE)`
- 第12层：LV1 -> LV0，JOIN条件索引：`(GROUP_CODE)`

## 优化效果

### 性能提升
1. **索引优化**：为每个临时表创建了基于JOIN条件的联合索引，大幅提升JOIN性能
2. **查询计划优化**：临时表使数据库优化器能够生成更优的执行计划
3. **内存使用优化**：避免重复扫描大表，减少内存占用
4. **并发性能**：临时表减少了对原表的锁定时间

### 可维护性提升
1. **代码结构清晰**：每层处理逻辑独立，便于理解和维护
2. **消除循环**：将FOR循环展开为独立语句，提高代码可读性
3. **错误定位**：每层独立处理，便于问题定位和调试
4. **资源管理**：明确的临时表创建和清理，避免资源泄漏

### 索引策略
根据JOIN条件创建的联合索引：
- **复杂JOIN**：`(GROUP_CODE, VIEW_FLAG, PARENT_CODE, CALIBER_FLAG)`
- **简单JOIN**：`(GROUP_CODE)`

## 注意事项
1. 临时表会占用额外的存储空间，但在处理完成后会自动清理
2. 索引创建需要额外时间，但JOIN性能提升远大于索引创建开销
3. 建议在生产环境部署前进行性能测试验证
