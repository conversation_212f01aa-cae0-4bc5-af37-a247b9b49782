-- Name: f_dm_foc_ict_item_dtl_inner; Type: Function; Schema: fin_dm_opt_foi;
/**
FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I
DWRDIM.DWR_DIM_MATERIAL_CODE_D
DMDIM.DM_DIM_PRODUCT_T_D
FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D
FIN_DM_OPT_FOI.DM_FOC_ENERGY_CATG_CEG_ICT_D
FIN_DM_OPT_FOI.DM_FOC_IAS_CATG_CEG_ICT_D
FIN_DM_OPT_FOI.DM_FOC_DIM_PRODUCTDIMENSION_D
FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T  
FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T
FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
FIN_DM_OPT_FOI.DM_FOC_CAL_LOG_T


**/
CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_ict_item_dtl_inner(f_year character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
-- session 表可加index
-- 
/*
最后修改人: SQL大师
背景描述：1. 专门处理ICT产业的实际发货明细表关联维表带出重量级团队,采购专家团字段
          2. 将单ITEM的品类数据条目打上标识
          3. 从原函数中拆分出ICT产业专用逻辑，常量内联化
参数描述：f_year: 年份参数
          x_result_status: 是否成功
事例：SELECT FIN_DM_OPT_FOI.f_dm_foc_ict_item_dtl_inner('202412')
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_ICT_ITEM_DTL_INNER'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION_ID2 BIGINT; 
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --清空ICT目标表数据:
  V_SQL := 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T WHERE PERIOD_ID = '||F_YEAR||';' ;
  EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除ICT表FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的'||F_YEAR||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- ICT产业版本管理逻辑
  -- 查询该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY';
        
  -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
    SELECT VERSION_ID INTO V_VERSION_ID
      FROM
          FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
      WHERE
          VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
          AND DEL_FLAG = 'N'
          AND STATUS = 1
          AND UPPER(DATA_TYPE) = 'CATEGORY';
  ELSE
    --新版本号赋值
    SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S')
      INTO V_VERSION_ID
      FROM DUAL;
       
    --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID
    INSERT INTO FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    (VERSION_ID,
     PARENT_VERSION_ID,
     VERSION,
     STATUS,
     VERSION_TYPE,
     DATA_TYPE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     IS_RUNNING)
     VALUES
     (V_VERSION_ID,NULL,V_VERSION_NAME,1,'AUTO','CATEGORY',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
  END IF;
  
  --查询品类专家团映射关系的最新版本号; 
  SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID2 
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1; 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往ICT版本信息表记录TOP品类版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --往ICT目标表里插数据
  V_SQL := '
  WITH CEG_TEMP AS (
   SELECT DISTINCT CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
	  L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME
	  FROM
   ( SELECT DISTINCT CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
	  L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME ,ROW_NUMBER() OVER(PARTITION BY CATEGORY_CODE,CATEGORY_CN_NAME  ORDER BY LABLE ASC) RANK
	  FROM
	  	 (SELECT VERSION_ID,CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
	  L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,''ENERGY'' AS LABLE FROM
	  FIN_DM_OPT_FOI.DM_FOC_ENERGY_CATG_CEG_ICT_D WHERE VERSION_ID = '''||V_DIM_VERSION_ID2||''' AND DEL_FLAG = ''N''
	  UNION ALL
	  SELECT VERSION_ID,CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
	  L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,''ICT'' AS LABLE  FROM
	  FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D WHERE VERSION_ID = '''||V_DIM_VERSION_ID2||''' AND DEL_FLAG = ''N''
	  )
	  )
	  WHERE RANK = 1
	  ),
	  CEG_IAS_TEMP AS (
	 SELECT VERSION_ID,CATEGORY_CODE,CATEGORY_CN_NAME,L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
	  L4_CEG_CODE,L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,''IAS'' AS LABLE  FROM
	  FIN_DM_OPT_FOI.DM_FOC_IAS_CATG_CEG_ICT_D WHERE VERSION_ID = '''||V_DIM_VERSION_ID2||''' AND DEL_FLAG = ''N''
	  )

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       LV3_PROD_RND_TEAM_CODE,
       LV3_PROD_RD_TEAM_CN_NAME,
       LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
       LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
       LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
       DIMENSION_CODE,  --量纲编码(9月需求调整)
       DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
       DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
       DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
       DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
       DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
       DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
       DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
       DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,--模块code(9月需求调整)
       L4_CEG_CN_NAME,--模块名称(9月需求调整)
       L4_CEG_SHORT_CN_NAME,--模块简称(9月需求调整)
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       SHIP_QUANTITY,
       RMB_COST_AMT,
       PROD_CODE, --ICT产业特有字段
       CN_DESCRIPTION,
       PARENTPARTNUMBER,
       PARENT_SHIP_QUANTITY,
       NON_SALE_FLAG,
       OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       PRIMARY_ID,
       L1_NAME,
       L2_NAME
       )
  SELECT     T.VERSION_ID,
             T.PERIOD_YEAR,
             T.PERIOD_ID, --年月
             T.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码
             T.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称
             T.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码
             T.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称
             T.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码
             T.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称
             T.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码
             T.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称
             T.LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
             T.LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
             T.LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
             T.DIMENSION_CODE,  --量纲编码(9月需求调整)
             T.DIMENSION_CN_NAME,  --量纲中文名称(9月需求调整)
             T.DIMENSION_EN_NAME,  --量纲英文名称(9月需求调整)
             T.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
             T.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
             T.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
             T.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
             T.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
             T.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
             T.L3_CEG_CODE, --专家团code
             T.L3_CEG_CN_NAME, --专家团名称
             T.L3_CEG_SHORT_CN_NAME, --专家团简称
             T.L4_CEG_CODE,--模块code(9月需求调整)
             T.L4_CEG_CN_NAME,--模块名称(9月需求调整)
             T.L4_CEG_SHORT_CN_NAME,--模块简称(9月需求调整)
             T.CATEGORY_CODE, --小类代码
             T.CATEGORY_CN_NAME, --小类名称
             T.ITEM_CODE, --子项ITEM编码
             T.ITEM_CN_NAME, --子项ITEM名称
             T.SHIP_QUANTITY, --子项ITEM发货量
             T.RMB_COST_AMT, --发货成本
             T.PROD_CODE, --ICT产业特有字段
             T.CN_DESCRIPTION, --产品名称
             T.PARENTPARTNUMBER, --父项ITEM编码
             T.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
             T.NON_SALE_FLAG, --非销售标识
             T.OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
             T.CREATED_BY,
             T.CREATION_DATE,
             T.LAST_UPDATED_BY,
             T.LAST_UPDATE_DATE,
             T.DEL_FLAG,
             T.PRIMARY_ID,
             T.L1_NAME, --盈利颗粒度L1层级
             CASE WHEN T.FLAG = 0 THEN NVL(L2S.L2_NAME,''其他'')
                  ELSE NVL(T.L2_NAME,''其他'')
                  END AS L2_NAME --盈利颗粒度L2层级
  FROM
  (
      SELECT '||V_VERSION_ID||' AS VERSION_ID,
             CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
             D.PERIOD_ID, --年月
             CP.LV0_PROD_RND_TEAM_CODE, --重量级团队LV0代码(9月需求调整)
             CP.LV0_PROD_RD_TEAM_CN_NAME, --重量级团队LV0中文名称(9月需求调整)
             CP.LV1_PROD_RND_TEAM_CODE, --重量级团队LV1代码(9月需求调整)
             CP.LV1_PROD_RD_TEAM_CN_NAME, --重量级团队LV1中文名称(9月需求调整)
             CP.LV2_PROD_RND_TEAM_CODE, --重量级团队LV2代码(9月需求调整)
             CP.LV2_PROD_RD_TEAM_CN_NAME, --重量级团队LV2中文名称(9月需求调整)
             CP.LV3_PROD_RND_TEAM_CODE, --重量级团队LV3代码(9月需求调整)
             CP.LV3_PROD_RD_TEAM_CN_NAME, --重量级团队LV3中文名称(9月需求调整)
             CASE WHEN CP.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                       CP.LV0_PROD_LIST_CODE ELSE ''其他''
                END AS LV0_PROD_LIST_CODE,--销售维度BG代码(9月需求调整)
             CASE WHEN CP.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                       CP.LV0_PROD_LIST_CN_NAME ELSE ''其他''
                END AS LV0_PROD_LIST_CN_NAME,--销售维度BG中文名称(9月需求调整)
             CASE WHEN CP.LV0_PROD_LIST_CODE IN ( ''PDCG901159'', ''PDCG817296'', ''PDCG901160'' ) THEN
                       CP.LV0_PROD_LIST_EN_NAME ELSE ''OTHER''
                END AS LV0_PROD_LIST_EN_NAME,--销售维度BG英文名称(9月需求调整)
             PD.PRODUCT_DIMENSION_CODE AS DIMENSION_CODE,  --产品量纲编码(9月需求调整)
             PD.PRODUCT_DIMENSION_CN_NAME AS DIMENSION_CN_NAME,  --产品量纲中文名称(9月需求调整)
             PD.PRODUCT_DIMENSION_EN_NAME AS DIMENSION_EN_NAME,  --产品量纲英文名称(9月需求调整)
             PD.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码(9月需求调整)
             PD.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称(9月需求调整)
             PD.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称(9月需求调整)
             PD.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码(9月需求调整)
             PD.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称(9月需求调整)
             PD.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称(9月需求调整)
             ZJ.L3_CEG_CODE, --专家团code
             ZJ.L3_CEG_CN_NAME, --专家团名称
             ZJ.L3_CEG_SHORT_CN_NAME, --专家团简称
             ZJ.L4_CEG_CODE,--模块code(9月需求调整)
             ZJ.L4_CEG_CN_NAME,--模块名称(9月需求调整)
             ZJ.L4_CEG_SHORT_CN_NAME,--模块简称(9月需求调整)
             M.ITEM_SUBTYPE_CODE AS CATEGORY_CODE, --小类代码
             M.ITEM_SUBTYPE_CN_NAME AS CATEGORY_CN_NAME, --小类名称
             M.ITEM_CODE, --子项ITEM编码
             M.ITEM_NAME AS ITEM_CN_NAME, --子项ITEM名称
             D.SHIP_QUANTITY, --子项ITEM发货量
             T.RMB_COST_AMT, --发货成本
             CP.PROD_CODE, --产品编码 - ICT特有
             CP.CN_DESCRIPTION, --产品名称
             D.PARENTPARTNUMBER, --SPART编码
             D.PARENT_SHIP_QUANTITY, --父项编码发货数量(缺少父项ITEM名称字段匹配规则)
             D.NON_SALE_FLAG, --非销售标识
             DECODE(RC.OVERSEA_FLAG,''Y'',''O'',''N'',''I'') AS OVERSEA_FLAG,--是否海外标识:Y是(海外O),N否(国内I)(9月需求调整)
             ''-1'' AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             ''-1'' AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             ''N'' AS DEL_FLAG,
             T.PRIMARY_ID,
             COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME, ''其他'') AS L1_NAME, --盈利颗粒度L1层级
             L2C.L2_NAME AS L2_NAME, --COA维表盈利颗粒度L2层级
             COUNT(L2C.L2_NAME) OVER(PARTITION BY COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME)) AS FLAG --标识COA维表L2_NAME为空的数据为0
        FROM (SELECT * FROM FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I  WHERE PERIOD_ID = '''||F_YEAR||'''
	  ) D
       INNER JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D M --物料维度表
          ON D.ITEM_CODE = M.ITEM_CODE
		  AND CAST(SUBSTR(TO_CHAR(D.PERIOD_ID), 1, 4) AS BIGINT) >= YEAR(CURRENT_TIMESTAMP)-3
       INNER JOIN DMDIM.DM_DIM_PRODUCT_D CP --重量级团队匹配维表(9月需求调整)
          ON D.PROD_KEY = CP.PROD_KEY
         AND CP.LV0_PROD_RND_TEAM_CODE = ''104364''  --ICT产业固定为IRB(104364)
         AND D.SHIP_QUANTITY >= 0 -- 数量非负数
       LEFT JOIN  DM_FOC_DIM_PRODUCTDIMENSION_D   PD--量纲维表(9月需求调整)
          ON D.MAIN_DIMENSION_KEY = PD.DIMENSION_KEY
       INNER JOIN CEG_TEMP ZJ --品类-专家团映射维
          ON M.ITEM_SUBTYPE_CODE = ZJ.CATEGORY_CODE
         AND D.MODEL_NUM IN (''P'', ''SI'') --取P和SI件
         AND D.RECOGNISE_TYPE_ID = 4 --取供应中心发区域（4）
         AND D.IS_RESALE_FLAG = ''N'' --去除转售重复数据(9月需求调整)
        LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T T --数据侧高斯加密表
          ON D.PRIMARY_ID = T.PRIMARY_ID
        LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1 --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
          ON CP.LV1_PROD_RND_TEAM_CODE = L1.LV1_CODE
         AND CP.LV2_PROD_RND_TEAM_CODE = L1.LV2_CODE
         AND CP.LV3_PROD_RND_TEAM_CODE = L1.LV3_CODE
         AND L1.LV3_CODE IS NOT NULL -- 先LV3_CODE有值的进行关联
         AND L1.DEL_FLAG = ''N'' AND UPPER(L1.STATUS) = ''SUBMIT''
        LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T L1B --ICT业务预测全景图,7月版本增加盈利颗粒度L1、L2层级
          ON CP.LV1_PROD_RND_TEAM_CODE = L1B.LV1_CODE
         AND CP.LV2_PROD_RND_TEAM_CODE = L1B.LV2_CODE
         AND L1B.LV2_CODE IS NOT NULL
         AND L1B.LV3_CODE IS NULL -- LV3_CODE无值时关联到LV2_CODE取值即可
         AND L1B.DEL_FLAG = ''N'' AND UPPER(L1B.STATUS) = ''SUBMIT''
        LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG -- 其他场景:重量级团队和L1_NAME关系的配置表
          ON CP.LV1_PROD_RD_TEAM_CN_NAME = CFG.LV1_PROD_RD_TEAM_CN_NAME
         AND CP.LV2_PROD_RD_TEAM_CN_NAME = CFG.LV2_PROD_RD_TEAM_CN_NAME
         AND CP.LV3_PROD_RD_TEAM_CN_NAME = CFG.LV3_PROD_RD_TEAM_CN_NAME
         AND CFG.LV3_PROD_RD_TEAM_CN_NAME IS NOT NULL -- 先LV3_PROD_RD_TEAM_CN_NAME有值的进行关联
        LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T CFG2 -- 其他场景:重量级团队和L1_NAME关系的配置表
          ON CP.LV1_PROD_RD_TEAM_CN_NAME = CFG2.LV1_PROD_RD_TEAM_CN_NAME
         AND CP.LV2_PROD_RD_TEAM_CN_NAME = CFG2.LV2_PROD_RD_TEAM_CN_NAME
         AND CFG2.LV2_PROD_RD_TEAM_CN_NAME IS NOT NULL
         AND CFG2.LV3_PROD_RD_TEAM_CN_NAME IS NULL -- LV3_PROD_RD_TEAM_CN_NAME无值时关联到LV2取值即可
        LEFT JOIN FIN_DM_OPT_FOP.APD_FOP_COA_L1_T L2C --COA维表
         ON COALESCE(L1.L1_NAME, L1B.L1_NAME, CFG.L1_NAME, CFG2.L1_NAME) = L2C.L1_NAME
        AND CP.PROD_CODE = L2C.COA_CODE
        AND L2C.DEL_FLAG = ''N'' AND UPPER(L2C.STATUS) = ''SUBMIT''
        LEFT JOIN DWRDIM.DWR_DIM_REGION_RC_D RC --(9月需求调整)国内海外区分通过主表关联COA区域维表，取OVERSEA_FLAG字段作为国内/海外标签
         ON D.GEO_PC_KEY = RC.GEO_PC_KEY

        ) T
        LEFT JOIN (SELECT DISTINCT ITEM_CODE,L1_NAME,L2_NAME FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T --Spart与L1、L2、L3关系表
                    WHERE DEL_FLAG = ''N''
                      AND UPPER(STATUS) = ''SUBMIT''  -- 状态（SAVE 保存、SUBMIT 提交）
                      AND PERIOD_ID = (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T)  --取最大的年月
                  ) L2S
         ON T.L1_NAME = L2S.L1_NAME
        AND T.PARENTPARTNUMBER = L2S.ITEM_CODE'
        ;
	DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE V_SQL;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到ICT表FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T, 新版本号='||V_VERSION_ID||', 所依赖的品类-专家团的版本号='||V_DIM_VERSION_ID2,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  V_SQL := 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
  EXECUTE IMMEDIATE V_SQL;

  --4.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集ICT表统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/
