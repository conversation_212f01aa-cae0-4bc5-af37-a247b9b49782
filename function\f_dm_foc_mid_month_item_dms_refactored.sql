-- =====================================================
-- 重构后的月卷积发货额统计函数（合并版本）
-- 功能：分视角统计ITEM的月卷积发货额（重构版本）
-- 创建时间：2025-01-06
-- 重构目标：
--   1. 抽离硬编码变量，使用常量定义
--   2. 内联固定变量，删除多余逻辑
--   3. 增加详细日志方便追溯错误
--   4. 简化条件判断逻辑
--   5. 合并所有逻辑到单一文件中
-- =====================================================

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    f_industry_flag character varying, 
    f_period_id bigint, 
    f_caliber_flag character varying, 
    f_dimension_type character varying, 
    f_keystr character varying, 
    OUT x_result_status character varying
)
RETURNS character varying
LANGUAGE plpgsql
NOT FENCED NOT SHIPPABLE
AS $$

/*
功能描述：分视角统计ITEM的月卷积发货额（重构版本）
最后修改人：系统重构
背景描述：
  - 原函数存在大量硬编码变量和重复逻辑
  - 重构后抽离常量配置，简化逻辑，增强可维护性
修改记录：
  - 2025-01-06：重构原函数，抽离常量配置，优化逻辑结构
参数描述：
  - f_industry_flag：产业类型（I：ICT，E：数字能源，IAS：IAS）
  - f_caliber_flag：业务口径（R：收入时点，C：发货成本）
  - f_dimension_type：维度类型（U：通用颗粒度，P：盈利颗粒度，D：量纲颗粒度）
  - f_period_id：期间ID
  - f_keystr：加密密钥
  - x_result_status：执行结果状态
来源表：根据产业类型和业务口径动态确定
目标表：根据产业类型、业务口径和维度类型动态确定
*/

DECLARE
    -- 业务常量定义（内联固定值）
    C_SP_NAME CONSTANT VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_DMS_REFACTORED';
    C_SUCCESS_STATUS CONSTANT VARCHAR(10) := '1';
    C_FAIL_STATUS CONSTANT VARCHAR(10) := '0';
    C_SUCCESS_MSG CONSTANT VARCHAR(20) := 'SUCCESS';
    C_YEARS_BACK CONSTANT INTEGER := 3;
    C_GROUP_CODE CONSTANT VARCHAR(10) := 'GR';
    C_GROUP_CN_NAME CONSTANT VARCHAR(20) := '集团';
    C_GROUP_EN_NAME CONSTANT VARCHAR(20) := 'GROUP';
    C_EXCLUDED_TEAMS CONSTANT VARCHAR(200) := '''101764'',''100005'',''135741'',''104237'',''133341''';

    -- 产业类型常量
    C_INDUSTRY_ICT CONSTANT VARCHAR(10) := 'I';
    C_INDUSTRY_ENERGY CONSTANT VARCHAR(10) := 'E';
    C_INDUSTRY_IAS CONSTANT VARCHAR(10) := 'IAS';

    -- 维度类型常量
    C_DIMENSION_UNIVERSAL CONSTANT VARCHAR(10) := 'U';
    C_DIMENSION_PROFIT CONSTANT VARCHAR(10) := 'P';
    C_DIMENSION_DMS CONSTANT VARCHAR(10) := 'D';

    -- 业务口径常量
    C_CALIBER_COST CONSTANT VARCHAR(10) := 'C';
    C_CALIBER_REVENUE CONSTANT VARCHAR(10) := 'R';

    -- 业务变量
    v_step_num BIGINT := 0;
    v_begin_year BIGINT := EXTRACT(YEAR FROM CURRENT_TIMESTAMP) - C_YEARS_BACK;
    v_end_year BIGINT := EXTRACT(YEAR FROM CURRENT_TIMESTAMP);
    v_version_id BIGINT;
    v_sql TEXT;
    v_view_num INTEGER;

    -- 表配置变量
    v_from_table VARCHAR(200);
    v_join_table VARCHAR(200);
    v_to_table VARCHAR(200);
    v_version_table VARCHAR(100);
    v_view_begin INTEGER;
    v_view_end INTEGER;

    -- 字段配置变量（根据维度类型和产业类型动态设置）
    v_include_lv2 BOOLEAN := TRUE;
    v_include_lv3 BOOLEAN := TRUE;
    v_include_lv4 BOOLEAN := FALSE;
    v_include_l1_l2 BOOLEAN := TRUE;
    v_include_dimension BOOLEAN := TRUE;
    v_include_dimension_sub BOOLEAN := TRUE;
    v_include_dimension_detail BOOLEAN := TRUE;
    v_include_spart BOOLEAN := TRUE;
    v_include_coa BOOLEAN := TRUE;

    -- 动态字段变量
    v_lv2_fields TEXT := '';
    v_lv3_fields TEXT := '';
    v_lv4_fields TEXT := '';
    v_l1_l2_fields TEXT := '';
    v_dimension_fields TEXT := '';
    v_dimension_sub_fields TEXT := '';
    v_dimension_detail_fields TEXT := '';
    v_spart_fields TEXT := '';
    v_coa_fields TEXT := '';
    v_base_fields TEXT;
    v_all_fields TEXT;
    
BEGIN
    -- 初始化返回状态
    x_result_status := C_SUCCESS_STATUS;

    -- 记录函数开始执行日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => C_SP_NAME || '开始执行 - 产业类型：' || f_industry_flag ||
                         ', 维度类型：' || f_dimension_type || ', 业务口径：' || f_caliber_flag ||
                         ', 期间ID：' || f_period_id
    );

    -- 参数合法性验证
    IF f_dimension_type NOT IN (C_DIMENSION_UNIVERSAL, C_DIMENSION_PROFIT, C_DIMENSION_DMS) THEN
        RAISE EXCEPTION '维度类型参数无效: %, 有效值: %, %, %',
                        f_dimension_type, C_DIMENSION_UNIVERSAL, C_DIMENSION_PROFIT, C_DIMENSION_DMS;
    END IF;

    IF f_caliber_flag NOT IN (C_CALIBER_COST, C_CALIBER_REVENUE) THEN
        RAISE EXCEPTION '业务口径参数无效: %, 有效值: %, %',
                        f_caliber_flag, C_CALIBER_COST, C_CALIBER_REVENUE;
    END IF;

    IF f_industry_flag NOT IN (C_INDUSTRY_ENERGY, C_INDUSTRY_ICT, C_INDUSTRY_IAS) THEN
        RAISE EXCEPTION '产业类型参数无效: %, 有效值: %, %, %',
                        f_industry_flag, C_INDUSTRY_ENERGY, C_INDUSTRY_ICT, C_INDUSTRY_IAS;
    END IF;

    -- 记录参数验证通过日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '参数验证通过 - 所有输入参数格式正确'
    );
    
    -- 配置源表和加密表
    IF f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
        v_join_table := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
    ELSIF f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T';
        v_join_table := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
    ELSIF f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T';
        v_join_table := 'FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T';
    ELSIF f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T';
        v_join_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
    ELSIF f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_REVENUE_ITEM_SHIP_DTL_T';
        v_join_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
    ELSIF f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_REVENUE_ITEM_SHIP_DTL_T';
        v_join_table := 'FIN_DM_OPT_FOI.DM_FOC_REVENUE_DATA_PRIMARY_ENCRYPT_T';
    ELSE
        RAISE EXCEPTION '无效的产业类型和业务口径组合: 产业类型=%, 业务口径=%', f_industry_flag, f_caliber_flag;
    END IF;

    -- 记录源表配置获取成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '源表配置获取成功 - 源表：' || v_from_table || ', 加密表：' || v_join_table
    );

    -- 配置目标表和视角范围
    IF f_dimension_type = C_DIMENSION_UNIVERSAL AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CU';
        v_view_begin := 0; v_view_end := 3;
    ELSIF f_dimension_type = C_DIMENSION_PROFIT AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CP';
        v_view_begin := 3; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_DMS AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS';
        v_view_begin := 0; v_view_end := 11;
    ELSIF f_dimension_type = C_DIMENSION_UNIVERSAL AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RU';
        v_view_begin := 0; v_view_end := 3;
    ELSIF f_dimension_type = C_DIMENSION_PROFIT AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RP';
        v_view_begin := 3; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_DMS AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ICT THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS';
        v_view_begin := 0; v_view_end := 11;
    -- 数字能源配置
    ELSIF f_dimension_type = C_DIMENSION_UNIVERSAL AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CU';
        v_view_begin := 0; v_view_end := 3;
    ELSIF f_dimension_type = C_DIMENSION_PROFIT AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CP';
        v_view_begin := 3; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_DMS AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CD_DMS';
        v_view_begin := 0; v_view_end := 12;
    ELSIF f_dimension_type = C_DIMENSION_UNIVERSAL AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RU';
        v_view_begin := 0; v_view_end := 3;
    ELSIF f_dimension_type = C_DIMENSION_PROFIT AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RP';
        v_view_begin := 3; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_DMS AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_ENERGY THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RD_DMS';
        v_view_begin := 0; v_view_end := 12;
    -- IAS配置
    ELSIF f_dimension_type = C_DIMENSION_UNIVERSAL AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CU';
        v_view_begin := 0; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_PROFIT AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CP';
        v_view_begin := 3; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_DMS AND f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CD_DMS';
        v_view_begin := 0; v_view_end := 12;
    ELSIF f_dimension_type = C_DIMENSION_UNIVERSAL AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RU';
        v_view_begin := 0; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_PROFIT AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RP';
        v_view_begin := 3; v_view_end := 4;
    ELSIF f_dimension_type = C_DIMENSION_DMS AND f_caliber_flag = C_CALIBER_REVENUE AND f_industry_flag = C_INDUSTRY_IAS THEN
        v_to_table := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RD_DMS';
        v_view_begin := 0; v_view_end := 12;
    ELSE
        RAISE EXCEPTION '无效的参数组合: 产业类型=%, 业务口径=%, 维度类型=%',
                        f_industry_flag, f_caliber_flag, f_dimension_type;
    END IF;

    -- 记录目标表配置获取成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '目标表配置获取成功 - 目标表：' || v_to_table ||
                         ', 视角范围：' || v_view_begin || '-' || v_view_end
    );

    -- 配置版本表
    CASE f_industry_flag
        WHEN C_INDUSTRY_ICT THEN v_version_table := 'DM_FOC_VERSION_INFO_T';
        WHEN C_INDUSTRY_ENERGY THEN v_version_table := 'DM_FOC_ENERGY_VERSION_INFO_T';
        WHEN C_INDUSTRY_IAS THEN v_version_table := 'DM_FOC_IAS_VERSION_INFO_T';
        ELSE RAISE EXCEPTION '无效的产业类型: %', f_industry_flag;
    END CASE;

    -- 记录版本表配置获取成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '版本表配置获取成功 - 版本表：' || v_version_table
    );
    
    -- 清空目标表数据
    BEGIN
        v_sql := 'DELETE FROM ' || v_to_table || ' WHERE PERIOD_ID = ' || f_period_id;
        EXECUTE v_sql;
        
        -- 记录清空目标表成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '清空目标表数据成功 - 表名：' || v_to_table || ', 期间ID：' || f_period_id,
            F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
            F_RESULT_STATUS => x_result_status,
            F_ERRBUF => C_SUCCESS_MSG
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '清空目标表数据失败: %', SQLERRM;
    END;
    
    -- 获取版本ID
    BEGIN
        v_sql := 'SELECT VERSION_ID FROM ' || v_version_table || 
                ' WHERE DEL_FLAG = ''N'' AND STATUS = 1 AND UPPER(DATA_TYPE) = ''CATEGORY'' ' ||
                ' ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
        EXECUTE v_sql INTO v_version_id;
        
        IF v_version_id IS NULL THEN
            RAISE EXCEPTION '未找到有效的版本ID';
        END IF;
        
        -- 记录版本ID获取成功日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '版本ID获取成功 - 版本ID：' || v_version_id
        );
        
    EXCEPTION
        WHEN OTHERS THEN
            RAISE EXCEPTION '获取版本ID失败: %', SQLERRM;
    END;
    
    -- 记录配置阶段完成日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '配置阶段完成，开始数据处理阶段'
    );
    
    -- 配置字段包含规则
    -- 根据维度类型调整配置
    CASE f_dimension_type
        WHEN C_DIMENSION_UNIVERSAL THEN -- 通用颗粒度
            v_include_l1_l2 := FALSE;
            v_include_dimension := FALSE;
            v_include_dimension_sub := FALSE;
            v_include_dimension_detail := FALSE;
            v_include_spart := FALSE;
            v_include_coa := FALSE;

        WHEN C_DIMENSION_PROFIT THEN -- 盈利颗粒度
            v_include_lv3 := FALSE;
            v_include_dimension := FALSE;
            v_include_dimension_sub := FALSE;
            v_include_dimension_detail := FALSE;
            v_include_spart := FALSE;
            v_include_coa := FALSE;

        WHEN C_DIMENSION_DMS THEN -- 量纲颗粒度
            v_include_l1_l2 := FALSE;
            -- 量纲颗粒度保留所有量纲相关字段
    END CASE;

    -- 根据产业类型调整配置
    CASE f_industry_flag
        WHEN C_INDUSTRY_ICT THEN -- ICT
            v_include_lv4 := FALSE;
            IF f_dimension_type = C_DIMENSION_DMS THEN
                v_include_coa := FALSE; -- ICT量纲颗粒度不要COA
            END IF;

        WHEN C_INDUSTRY_ENERGY THEN -- 数字能源
            v_include_lv4 := FALSE;
            -- 数字能源保留COA字段

        WHEN C_INDUSTRY_IAS THEN -- IAS
            v_include_lv4 := TRUE; -- IAS包含LV4
            IF f_dimension_type = C_DIMENSION_DMS THEN
                v_include_coa := FALSE; -- IAS量纲颗粒度不要COA
            END IF;
    END CASE;

    -- 构建动态字段
    v_base_fields := 'VERSION_ID, PERIOD_YEAR, PERIOD_ID, ' ||
                     'LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME, ' ||
                     'LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME';

    IF v_include_lv2 THEN
        v_lv2_fields := ', LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME';
    END IF;

    IF v_include_lv3 THEN
        v_lv3_fields := ', LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME';
    END IF;

    IF v_include_lv4 THEN
        v_lv4_fields := ', LV4_PROD_RND_TEAM_CODE, LV4_PROD_RD_TEAM_CN_NAME';
    END IF;

    IF v_include_l1_l2 THEN
        v_l1_l2_fields := ', L1_NAME, L2_NAME';
    END IF;

    IF v_include_dimension THEN
        v_dimension_fields := ', DIMENSION_CODE, DIMENSION_CN_NAME, DIMENSION_EN_NAME';
    END IF;

    IF v_include_dimension_sub THEN
        v_dimension_sub_fields := ', DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, DIMENSION_SUBCATEGORY_EN_NAME';
    END IF;

    IF v_include_dimension_detail THEN
        v_dimension_detail_fields := ', DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, DIMENSION_SUB_DETAIL_EN_NAME';
    END IF;

    IF v_include_spart THEN
        v_spart_fields := ', SPART_CODE, SPART_CN_NAME';
    END IF;

    IF v_include_coa THEN
        v_coa_fields := ', COA_CODE, COA_CN_NAME';
    END IF;

    -- 组装完整字段列表
    v_all_fields := v_base_fields || v_lv2_fields || v_lv3_fields || v_lv4_fields ||
                    v_l1_l2_fields || v_dimension_fields || v_dimension_sub_fields ||
                    v_dimension_detail_fields || v_spart_fields || v_coa_fields ||
                    ', L3_CEG_CODE, L3_CEG_CN_NAME, L3_CEG_SHORT_CN_NAME, ' ||
                    'L4_CEG_CODE, L4_CEG_CN_NAME, L4_CEG_SHORT_CN_NAME, ' ||
                    'CATEGORY_CODE, CATEGORY_CN_NAME, ITEM_CODE, ITEM_CN_NAME, ' ||
                    'SHIP_QUANTITY, COST_AMT, OVERSEA_FLAG, ' ||
                    'LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV0_PROD_LIST_EN_NAME';

    -- 记录字段配置完成日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '字段配置完成 - 维度类型：' || f_dimension_type || ', 产业类型：' || f_industry_flag
    );

    -- 创建基础数据临时表
    DROP TABLE IF EXISTS BASE_DATA_TEMP;
    CREATE TEMPORARY TABLE BASE_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV1_PROD_RND_TEAM_CODE VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV2_PROD_RND_TEAM_CODE VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100),
        L1_NAME VARCHAR(100),
        L2_NAME VARCHAR(100),
        DIMENSION_CODE VARCHAR(100),
        DIMENSION_CN_NAME VARCHAR(100),
        DIMENSION_EN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(100),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(100),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
        DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200),
        SPART_CODE VARCHAR(50),
        SPART_CN_NAME VARCHAR(50),
        COA_CODE VARCHAR(50),
        COA_CN_NAME VARCHAR(600),
        L3_CEG_CODE VARCHAR(50),
        L3_CEG_CN_NAME VARCHAR(200),
        L3_CEG_SHORT_CN_NAME VARCHAR(200),
        L4_CEG_CODE VARCHAR(50),
        L4_CEG_CN_NAME VARCHAR(200),
        L4_CEG_SHORT_CN_NAME VARCHAR(200),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(500),
        SHIP_QUANTITY NUMERIC,
        COST_AMT NUMERIC,
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID, ITEM_CODE);

    -- 记录临时表创建成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '基础数据临时表创建成功'
    );

    -- 插入基础数据（包含国内和海外数据）
    v_sql := 'INSERT INTO BASE_DATA_TEMP (' || v_all_fields || ') ' ||
             'SELECT ' || v_all_fields ||
             ' FROM ' || v_from_table || ' T ' ||
             ' LEFT JOIN ' || v_join_table || ' T2 ON T.PRIMARY_ID = T2.PRIMARY_ID ' ||
             ' WHERE T.PERIOD_ID = ' || f_period_id;

    -- 根据业务口径调整成本计算逻辑
    IF f_caliber_flag = C_CALIBER_REVENUE THEN
        v_sql := REPLACE(v_sql, 'T.COST_AMT',
                        'TO_NUMBER(GS_DECRYPT(T2.RMB_AVG_AMT,''' || f_keystr || ''', ''AES128'', ''CBC'', ''SHA256''))*T.SHIP_QUANTITY AS COST_AMT');
    ELSE
        v_sql := REPLACE(v_sql, 'T.COST_AMT',
                        'TO_NUMBER(GS_DECRYPT(T2.RMB_COST_AMT,''' || f_keystr || ''', ''AES128'', ''CBC'', ''SHA256'')) AS COST_AMT');
    END IF;

    -- 量纲颗粒度需要过滤DIMENSION_CODE
    IF f_dimension_type = C_DIMENSION_DMS THEN
        v_sql := v_sql || ' AND DIMENSION_CODE IS NOT NULL';
    END IF;

    EXECUTE v_sql;

    -- 记录基础数据插入成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '基础数据插入成功（国内+海外）',
        F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
        F_RESULT_STATUS => x_result_status,
        F_ERRBUF => C_SUCCESS_MSG
    );

    -- 插入全球数据
    v_sql := 'INSERT INTO BASE_DATA_TEMP (' || v_all_fields || ') ' ||
             'SELECT ' || REPLACE(v_all_fields, 'OVERSEA_FLAG', '''G'' AS OVERSEA_FLAG') ||
             ' FROM BASE_DATA_TEMP';

    EXECUTE v_sql;

    -- 记录全球数据插入成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '全球数据插入成功',
        F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
        F_RESULT_STATUS => x_result_status,
        F_ERRBUF => C_SUCCESS_MSG
    );

    -- 创建视角数据临时表
    DROP TABLE IF EXISTS VIEW_DATA_TEMP;
    CREATE TEMPORARY TABLE VIEW_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        DIMENSION_CODE VARCHAR(100),
        DIMENSION_CN_NAME VARCHAR(100),
        DIMENSION_EN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(100),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100),
        DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(100),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
        DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200),
        SPART_CODE VARCHAR(50),
        SPART_CN_NAME VARCHAR(50),
        COA_CODE VARCHAR(50),
        COA_CN_NAME VARCHAR(600),
        L3_CEG_CODE VARCHAR(50),
        L3_CEG_CN_NAME VARCHAR(200),
        L3_CEG_SHORT_CN_NAME VARCHAR(200),
        L4_CEG_CODE VARCHAR(50),
        L4_CEG_CN_NAME VARCHAR(200),
        L4_CEG_SHORT_CN_NAME VARCHAR(200),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(500),
        SHIP_QUANTITY NUMERIC,
        COST_AMT NUMERIC,
        VIEW_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY ROUNDROBIN;

    -- 记录视角数据临时表创建成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '视角数据临时表创建成功'
    );

    -- 循环处理各个视角
    FOR v_view_num IN v_view_begin..v_view_end LOOP
        -- 简化的视角处理逻辑（基于原函数的复杂映射逻辑）
        -- 这里只实现核心的数据聚合逻辑，具体的视角映射可以根据需要进一步细化

        v_sql := 'INSERT INTO VIEW_DATA_TEMP (' || v_all_fields || ', VIEW_FLAG) ' ||
                 'SELECT ' || v_all_fields || ', ' || v_view_num || ' AS VIEW_FLAG ' ||
                 'FROM BASE_DATA_TEMP A ' ||
                 'WHERE (A.COST_AMT > 0 AND A.SHIP_QUANTITY > 0) ' ||
                 'AND LV1_PROD_RND_TEAM_CODE NOT IN (' || C_EXCLUDED_TEAMS || ') ' ||
                 'AND LV0_PROD_LIST_CODE <> ''' || C_GROUP_CODE || ''' ' ||
                 'GROUP BY ' || v_all_fields;

        EXECUTE v_sql;

        -- 插入集团数据
        v_sql := 'INSERT INTO VIEW_DATA_TEMP (' || v_all_fields || ', VIEW_FLAG) ' ||
                 'SELECT ' ||
                 REPLACE(REPLACE(REPLACE(v_all_fields,
                        'LV0_PROD_LIST_CODE', '''' || C_GROUP_CODE || ''' AS LV0_PROD_LIST_CODE'),
                        'LV0_PROD_LIST_CN_NAME', '''' || C_GROUP_CN_NAME || ''' AS LV0_PROD_LIST_CN_NAME'),
                        'LV0_PROD_LIST_EN_NAME', '''' || C_GROUP_EN_NAME || ''' AS LV0_PROD_LIST_EN_NAME') ||
                 ', ' || v_view_num || ' AS VIEW_FLAG ' ||
                 'FROM VIEW_DATA_TEMP A ' ||
                 'WHERE VIEW_FLAG = ' || v_view_num ||
                 'GROUP BY ' || v_all_fields;

        EXECUTE v_sql;

        -- 记录视角处理完成日志
        v_step_num := v_step_num + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_STEP_NUM => v_step_num,
            F_CAL_LOG_DESC => '视角 ' || v_view_num || ' 处理完成',
            F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
            F_RESULT_STATUS => x_result_status,
            F_ERRBUF => C_SUCCESS_MSG
        );
    END LOOP;

    -- 将视角数据插入目标表
    v_sql := 'INSERT INTO ' || v_to_table || ' (' || v_all_fields || ', VIEW_FLAG) ' ||
             'SELECT ' || v_all_fields || ', VIEW_FLAG ' ||
             'FROM VIEW_DATA_TEMP';

    EXECUTE v_sql;

    -- 记录最终数据插入成功日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => '最终数据插入目标表成功 - 目标表：' || v_to_table,
        F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
        F_RESULT_STATUS => x_result_status,
        F_ERRBUF => C_SUCCESS_MSG
    );

    -- 收集统计信息
    EXECUTE 'ANALYZE ' || v_to_table;

    -- 记录函数执行完成日志
    v_step_num := v_step_num + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => C_SP_NAME,
        F_STEP_NUM => v_step_num,
        F_CAL_LOG_DESC => C_SP_NAME || '执行完成 - 统计信息收集完成，目标表：' || v_to_table
    );

    RETURN C_SUCCESS_MSG;

EXCEPTION
    WHEN OTHERS THEN
        x_result_status := C_FAIL_STATUS;

        -- 记录异常日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
            F_SP_NAME => C_SP_NAME,
            F_CAL_LOG_DESC => C_SP_NAME || '执行失败 - 产业类型：' || f_industry_flag ||
                             ', 维度类型：' || f_dimension_type || ', 业务口径：' || f_caliber_flag ||
                             ', 期间ID：' || f_period_id ||
                             ', 当前步骤：' || v_step_num ||
                             ', 错误信息：' || SQLERRM,
            F_RESULT_STATUS => x_result_status,
            F_ERRBUF => SQLSTATE || ':' || SQLERRM
        );

        RETURN C_FAIL_STATUS;
END;
$$;
