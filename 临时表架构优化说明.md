# 临时表架构优化说明

## 修改概述

根据您的要求，已将临时表的创建方式从 `CREATE TEMP TABLE ... AS SELECT` 改为先创建表结构，然后插入数据的方式。这种方式提供了更好的控制和性能优化空间。

## 修改前后对比

### 修改前（CREATE AS SELECT方式）
```sql
-- 一步创建临时表并插入数据
CREATE TEMP TABLE LEV_INDEX_TEMP AS 
SELECT VIEW_FLAG, PROD_RND_TEAM_CODE, ...
FROM SOURCE_TABLE
WHERE CONDITIONS;
```

### 修改后（先CREATE后INSERT方式）
```sql
-- 第1步：创建临时表结构
CREATE TEMPORARY TABLE LEV_INDEX_TEMP (
  VIEW_FLAG VARCHAR(20),
  PROD_RND_TEAM_CODE VARCHAR(50),
  PROD_RND_TEAM_CN_NAME VARCHAR(200),
  -- ... 其他字段定义
) ON COMMIT PRESERVE ROWS;

-- 第2步：插入数据
INSERT INTO LEV_INDEX_TEMP
SELECT VIEW_FLAG, PROD_RND_TEAM_CODE, ...
FROM SOURCE_TABLE  
WHERE CONDITIONS;
```

## 关键改进点

### 1. 表结构控制
- **明确字段类型**：每个字段都有明确的数据类型和长度定义
- **ON COMMIT PRESERVE ROWS**：确保事务提交后临时表数据保持可用
- **更好的内存管理**：数据库可以预先分配合适的存储空间

### 2. 性能优化空间
- **索引策略**：可以在表创建后、数据插入前创建索引
- **统计信息**：数据库可以更好地收集和利用统计信息
- **查询优化**：优化器可以更准确地估算成本

### 3. 错误处理
- **分步执行**：可以分别处理表创建和数据插入的错误
- **资源控制**：更精确地控制临时表的生命周期

## 具体实现细节

### LEV_INDEX_TEMP 临时表
```sql
-- 步骤4：创建LEV_INDEX_TEMP表结构
CREATE TEMPORARY TABLE LEV_INDEX_TEMP (
  VIEW_FLAG VARCHAR(20),
  PROD_RND_TEAM_CODE VARCHAR(50),
  PROD_RND_TEAM_CN_NAME VARCHAR(200),
  DIMENSION_CODE VARCHAR(50),
  DIMENSION_CN_NAME VARCHAR(200),
  DIMENSION_SUBCATEGORY_CODE VARCHAR(50),
  DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
  DIMENSION_SUB_DETAIL_CODE VARCHAR(50),
  DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
  SPART_CODE VARCHAR(50),
  SPART_CN_NAME VARCHAR(200),
  DMS_CODE VARCHAR(50),
  DMS_CN_NAME VARCHAR(200),
  PERIOD_YEAR INT,
  PERIOD_ID INT,
  MONTH_DAY VARCHAR(2),
  PARENT_CODE VARCHAR(50),
  PARENT_CN_NAME VARCHAR(200),
  GROUP_CODE VARCHAR(50),
  GROUP_CN_NAME VARCHAR(200),
  GROUP_LEVEL INT,
  COST_INDEX DECIMAL(18,6),
  SCENARIO_FLAG VARCHAR(10),
  CALIBER_FLAG VARCHAR(10),
  OVERSEA_FLAG VARCHAR(10),
  LV0_PROD_LIST_CODE VARCHAR(50),
  LV0_PROD_LIST_CN_NAME VARCHAR(200)
) ON COMMIT PRESERVE ROWS;

-- 步骤5：插入数据
INSERT INTO LEV_INDEX_TEMP SELECT ...;

-- 步骤6：创建索引
CREATE INDEX idx_lev_index_temp_main ON LEV_INDEX_TEMP (...);
```

### BASE_YOY_TEMP 临时表
```sql
-- 步骤7：创建BASE_YOY_TEMP表结构
CREATE TEMPORARY TABLE BASE_YOY_TEMP (
  VIEW_FLAG VARCHAR(20),
  PERIOD_YEAR INT,
  PERIOD_ID INT,
  -- ... 基础字段
  YOY_PERIOD_ID INT,           -- 同比期间ID
  YOY_COST_INDEX DECIMAL(18,6), -- 同比成本指数
  POP_PERIOD_ID INT,           -- 环比期间ID  
  POP_COST_INDEX DECIMAL(18,6), -- 环比成本指数
  -- ... 其他字段
) ON COMMIT PRESERVE ROWS;

-- 步骤8：插入数据（包含LAG窗口函数计算）
INSERT INTO BASE_YOY_TEMP SELECT ... WITH LAG FUNCTIONS;

-- 步骤9：创建索引
CREATE INDEX idx_base_yoy_temp_main ON BASE_YOY_TEMP (...);
```

## 执行流程优化

### 新的执行步骤
1. **步骤1-3**：初始化和数据清理
2. **步骤4**：创建LEV_INDEX_TEMP表结构
3. **步骤5**：插入LEV_INDEX_TEMP数据
4. **步骤6**：为LEV_INDEX_TEMP创建索引
5. **步骤7**：创建BASE_YOY_TEMP表结构
6. **步骤8**：插入BASE_YOY_TEMP数据（含LAG计算）
7. **步骤9**：为BASE_YOY_TEMP创建索引
8. **步骤10-11**：插入同比和环比数据到目标表
9. **步骤12**：清理临时表

### 日志记录增强
每个关键步骤都有对应的日志记录：
- 临时表创建完成
- 数据插入完成（含行数统计）
- 索引创建完成
- 最终数据插入完成

## 优势分析

### 1. 性能优势
- **预分配空间**：数据库可以根据字段定义预分配合适的存储空间
- **索引效率**：在数据插入后立即创建索引，避免数据变更时的索引维护开销
- **统计信息**：更准确的表统计信息有助于查询优化

### 2. 可维护性
- **清晰的结构**：表结构定义一目了然
- **分步调试**：可以分别调试表创建、数据插入、索引创建等步骤
- **错误定位**：更容易定位具体哪个步骤出现问题

### 3. 扩展性
- **字段修改**：需要调整字段时只需修改CREATE语句
- **约束添加**：可以在表创建时添加约束条件
- **分区支持**：为将来的分区表改造预留空间

## 注意事项

### 1. 事务管理
- 使用 `ON COMMIT PRESERVE ROWS` 确保事务提交后数据保持
- 异常处理中包含临时表清理逻辑

### 2. 内存使用
- 明确的字段类型定义有助于内存优化
- 大数据量时需要关注临时空间使用情况

### 3. 并发控制
- 临时表是会话级别的，不会影响其他会话
- 但需要注意同一会话内的资源竞争

## 总结

通过将临时表创建方式改为先CREATE后INSERT的模式，我们获得了：
- 更好的性能控制能力
- 更清晰的代码结构
- 更强的错误处理能力
- 更好的可维护性和扩展性

这种架构为后续的性能优化和功能扩展提供了更好的基础。
