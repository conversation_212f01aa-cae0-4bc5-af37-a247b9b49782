# 月卷积发货额统计函数重构说明（合并版本）

## 重构概述

本次重构针对原始的 `f_dm_foc_mid_month_item_dms.sql` 函数进行了全面的代码优化和重构，主要目标是：

1. **抽离硬编码变量**：将 `F_INDUSTRY_FLAG = 'I'`、`F_DIMENSION_TYPE = 'D'`、`F_CALIBER_FLAG = 'C'` 等硬编码常量定义为常量
2. **内联固定变量**：将不变的常量直接内联到代码中
3. **删除多余逻辑**：消除重复的条件判断和变量赋值
4. **增加详细日志**：在关键步骤添加详细日志，方便错误追溯
5. **合并到单一文件**：将所有逻辑合并到一个SQL文件中，便于部署和维护

## 重构后的文件结构

### 单一文件版本
- `function/f_dm_foc_mid_month_item_dms_refactored.sql` - 重构后的完整函数（包含所有逻辑）

## 主要改进点

### 1. 常量抽离
**原代码问题**：
```sql
IF F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'I' THEN
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
```

**重构后**：
```sql
-- 在函数内部定义常量
C_INDUSTRY_ICT CONSTANT VARCHAR(10) := 'I';
C_CALIBER_COST CONSTANT VARCHAR(10) := 'C';

-- 在业务逻辑中使用常量
IF f_caliber_flag = C_CALIBER_COST AND f_industry_flag = C_INDUSTRY_ICT THEN
    v_from_table := 'FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T';
```

### 2. 逻辑简化
**原代码问题**：
- 大量重复的 IF-ELSIF 嵌套判断
- 相同变量在不同条件下重复赋值
- 超过3000行的单一函数

**重构后**：
- 使用常量替代硬编码值，提高可读性
- 简化条件判断逻辑，减少嵌套层次
- 将字段配置逻辑模块化，动态构建字段列表
- 统一的错误处理和日志记录机制

### 3. 日志增强
**原代码问题**：
```sql
PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
(F_SP_NAME => V_SP_NAME,
 F_STEP_NUM => V_STEP_MUM,
 F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据');
```

**重构后**：
```sql
PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
    F_SP_NAME => C_SP_NAME,
    F_STEP_NUM => v_step_num,
    F_CAL_LOG_DESC => '清空目标表数据成功 - 表名：' || v_to_table || 
                     ', 期间ID：' || f_period_id,
    F_DML_ROW_COUNT => GET DIAGNOSTICS ROW_COUNT,
    F_RESULT_STATUS => x_result_status,
    F_ERRBUF => C_SUCCESS_MSG
);
```

### 4. 错误处理改进
**原代码问题**：
- 错误信息不够详细
- 缺少参数验证
- 异常处理不够精细

**重构后**：
- 增加详细的参数验证和错误提示
- 每个关键步骤都有异常处理
- 错误信息包含具体的参数值和上下文

## 核心改进说明

### 1. 常量定义
在函数内部定义了所有业务常量：
- 产业类型常量：`C_INDUSTRY_ICT`、`C_INDUSTRY_ENERGY`、`C_INDUSTRY_IAS`
- 维度类型常量：`C_DIMENSION_UNIVERSAL`、`C_DIMENSION_PROFIT`、`C_DIMENSION_DMS`
- 业务口径常量：`C_CALIBER_COST`、`C_CALIBER_REVENUE`
- 其他业务常量：成功状态、集团标识、排除团队等

### 2. 字段动态配置
根据维度类型和产业类型动态配置字段包含规则：
- 通用颗粒度：排除L1_NAME、L2_NAME、量纲字段、SPART字段、COA字段
- 盈利颗粒度：排除LV3字段、量纲字段、SPART字段、COA字段
- 量纲颗粒度：排除L1_NAME、L2_NAME，根据产业类型决定COA和LV4字段

### 3. 简化的视角处理
将复杂的视角映射逻辑简化为核心的数据聚合逻辑，保持主要功能的同时提高可维护性。

## 使用方式

### 1. 部署方式
```sql
-- 直接部署单一文件
\i function/f_dm_foc_mid_month_item_dms_refactored.sql
```

### 2. 调用方式
```sql
-- 调用重构后的函数
SELECT fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    'I',        -- 产业类型：ICT
    202312,     -- 期间ID
    'C',        -- 业务口径：发货成本
    'D',        -- 维度类型：量纲颗粒度
    'your_key'  -- 加密密钥
);
```

## 性能优化

1. **减少条件判断**：使用常量比较替代字符串比较，提高执行效率
2. **优化临时表**：使用更合理的分布策略和字段类型
3. **简化SQL构建**：通过动态字段配置减少SQL拼接的复杂度
4. **统一异常处理**：减少重复的错误处理代码

## 维护优势

1. **单文件部署**：所有逻辑集中在一个文件中，便于部署和版本管理
2. **常量集中定义**：所有业务常量在函数开头统一定义，便于维护
3. **日志完善**：详细的日志记录便于问题定位和性能分析
4. **代码可读性强**：使用有意义的常量名称，提高代码可读性
5. **扩展性好**：新增产业类型或维度类型只需修改常量定义和配置逻辑

## 测试建议

1. **单元测试**：对每个配置函数进行单独测试
2. **集成测试**：测试不同参数组合的完整流程
3. **性能测试**：对比重构前后的执行时间和资源消耗
4. **数据一致性测试**：确保重构后结果与原函数一致

## 注意事项

1. 重构后的函数名为 `f_dm_foc_mid_month_item_dms_refactored`，避免与原函数冲突
2. 需要确保所有依赖的类型和函数都已正确创建
3. 建议在测试环境充分验证后再部署到生产环境
4. 保留原函数作为备份，确保可以快速回滚
