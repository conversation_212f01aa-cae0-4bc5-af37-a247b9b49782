INSERT INTO DM_REPL_APPEND_AVG_TEMP (
    LV0_PROD_LIST_CODE,
    LV1_PROD_LIST_CODE,
    LV2_PROD_LIST_CODE,
    LV3_PROD_LIST_CODE,
    LV4_PROD_LIST_CODE,
    LV0_PROD_LIST_CN_NAME,
    LV1_PROD_LIST_CN_NAME,
    LV2_PROD_LIST_CN_NAME,
    LV3_PROD_LIST_CN_NAME,
    LV4_PROD_LIST_CN_NAME,
    DIMENSION_CODE,
    DIMENSION_CN_NAME,
    DIMENSION_SUBCATEGORY_CODE,
    DIMENSION_SUBCATEGORY_CN_NAME,
    DIMENSION_SUB_DETAIL_CODE,
    DIMENSION_SUB_DETAIL_CN_NAME,
    VIEW_FLAG,
    CODE_ATTRIBUTES,
    MAIN_FLAG,
    SPART_CODE,
    SPART_CN_NAME,
    R<PERSON><PERSON>_CODE,
    REGION_CN_NAME,
    REPOFFICE_CODE,
    REPOFFICE_CN_NAME,
    BG_CODE,
    BG_CN_NAME,
    OVERSEA_FLAG,
    PERIOD_YEAR,
    PERIOD_ID,
    RMB_AVG_AMT,
    ACTUAL_QTY,
    RMB_COST_AMT,
    APPEND_FLAG,
    SOFTWARE_MARK
  ) WITH ACTUAL_SPART_DIM_TEMP AS(
    --取全量PBI及SPART层级维度
    SELECT DISTINCT LV0_CODE,
      LV1_CODE,
      LV2_CODE,
      LV3_CODE,
      LV4_CODE,
      LV0_CN_NAME,
      LV1_CN_NAME,
      LV2_CN_NAME,
      LV3_CN_NAME,
      LV4_CN_NAME,
      DIMENSION_CODE,
      DIMENSION_CN_NAME,
      DIMENSION_SUBCATEGORY_CODE,
      DIMENSION_SUBCATEGORY_CN_NAME,
      DIMENSION_SUB_DETAIL_CODE,
      DIMENSION_SUB_DETAIL_CN_NAME,
      SPART_CODE,
      SPART_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
      CODE_ATTRIBUTES,
      MAIN_FLAG,
      VIEW_FLAG,
      SOFTWARE_MARK
    FROM DM_REPL_YTD_AVG_TEMP
  ),
  PERIOD_DIM_TEMP AS (
    --生成连续月份, 一年前首月至当前系统实际月, (当前系统实际月 = 当前系统月-1)
    SELECT CAST(
        TO_CHAR(
          ADD_MONTHS('2023-01-01 00:00:00', NUM.VAL - 1),
          'YYYYMM'
        ) AS BIGINT
      ) AS PERIOD_ID
    FROM GENERATE_SERIES(
        1,
        TO_NUMBER(
          TIMESTAMPDIFF(
            MONTH,
            '2023-01-01 00:00:00',
            CURRENT_TIMESTAMP
          )
        ),
        1
      ) NUM(VAL)
  ),
  CROSS_JOIN_TEMP AS (
    --生成连续年月的发散维
    SELECT CAST(SUBSTR(T2.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
      T2.PERIOD_ID,
      T1.LV0_CODE,
      T1.LV1_CODE,
      T1.LV2_CODE,
      T1.LV3_CODE,
      T1.LV4_CODE,
      T1.LV0_CN_NAME,
      T1.LV1_CN_NAME,
      T1.LV2_CN_NAME,
      T1.LV3_CN_NAME,
      T1.LV4_CN_NAME,
      T1.DIMENSION_CODE,
      T1.DIMENSION_CN_NAME,
      T1.DIMENSION_SUBCATEGORY_CODE,
      T1.DIMENSION_SUBCATEGORY_CN_NAME,
      T1.DIMENSION_SUB_DETAIL_CODE,
      T1.DIMENSION_SUB_DETAIL_CN_NAME,
      T1.SPART_CODE,
      T1.SPART_CN_NAME,
      T1.REGION_CODE,
      T1.REGION_CN_NAME,
      T1.REPOFFICE_CODE,
      T1.REPOFFICE_CN_NAME,
      T1.BG_CODE,
      T1.BG_CN_NAME,
      T1.OVERSEA_FLAG,
      T1.CODE_ATTRIBUTES,
      T1.MAIN_FLAG,
      T1.VIEW_FLAG,
      SOFTWARE_MARK
    FROM ACTUAL_SPART_DIM_TEMP T1,
      PERIOD_DIM_TEMP T2
  ),
  FORWARD_FILLER_TEMP AS (
    --按照所有维度，向前寻找会计期补齐均价
    SELECT SS.LV0_CODE,
      SS.LV1_CODE,
      SS.LV2_CODE,
      SS.LV3_CODE,
      SS.LV4_CODE,
      SS.LV0_CN_NAME,
      SS.LV1_CN_NAME,
      SS.LV2_CN_NAME,
      SS.LV3_CN_NAME,
      SS.LV4_CN_NAME,
      SS.DIMENSION_CODE,
      SS.DIMENSION_CN_NAME,
      SS.DIMENSION_SUBCATEGORY_CODE,
      SS.DIMENSION_SUBCATEGORY_CN_NAME,
      SS.DIMENSION_SUB_DETAIL_CODE,
      SS.DIMENSION_SUB_DETAIL_CN_NAME,
      SS.SPART_CODE,
      SS.SPART_CN_NAME,
      SS.VIEW_FLAG,
      SS.CODE_ATTRIBUTES,
      SS.SOFTWARE_MARK,
      SS.MAIN_FLAG,
      SS.REGION_CODE,
      SS.REGION_CN_NAME,
      SS.REPOFFICE_CODE,
      SS.REPOFFICE_CN_NAME,
      SS.BG_CODE,
      SS.BG_CN_NAME,
      SS.OVERSEA_FLAG,
      SS.PERIOD_YEAR,
      SS.PERIOD_ID,
      SS.RMB_AVG_AMT,
      SS.RMB_COST_AMT,
      FIRST_VALUE(SS.RMB_COST_AMT) OVER(
        PARTITION BY SS.LV0_CODE,
        SS.LV1_CODE,
        SS.LV2_CODE,
        SS.LV3_CODE,
        SS.LV4_CODE,
        NVL(SS.SPART_CODE, 'SPART'),
        NVL(SS.DIMENSION_CODE, 'DC'),
        NVL(SS.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
        NVL(SS.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
        SS.REGION_CODE,
        SS.REPOFFICE_CODE,
        SS.BG_CODE,
        SS.OVERSEA_FLAG,
        NVL(SS.CODE_ATTRIBUTES, 'CA'),
        NVL(SS.MAIN_FLAG, 'MF'),
        NVL(SS.SOFTWARE_MARK, 'SW'),
        NVL(SS.VIEW_FLAG, 'VF'),
        SS.AVG_AMT_FLAG
        ORDER BY SS.PERIOD_ID
      ) AS RMB_COST_AMT_2,
      --新补齐的均价字段
      FIRST_VALUE(SS.RMB_AVG_AMT) OVER(
        PARTITION BY SS.LV0_CODE,
        SS.LV1_CODE,
        SS.LV2_CODE,
        SS.LV3_CODE,
        SS.LV4_CODE,
        NVL(SS.SPART_CODE, 'SPART'),
        NVL(SS.DIMENSION_CODE, 'DC'),
        NVL(SS.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
        NVL(SS.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
        SS.REGION_CODE,
        SS.REPOFFICE_CODE,
        SS.BG_CODE,
        SS.OVERSEA_FLAG,
        NVL(SS.CODE_ATTRIBUTES, 'CA'),
        NVL(SS.MAIN_FLAG, 'MF'),
        NVL(SS.SOFTWARE_MARK, 'SW'),
        NVL(SS.VIEW_FLAG, 'VF'),
        SS.AVG_AMT_FLAG
        ORDER BY SS.PERIOD_ID
      ) AS RMB_AVG_AMT_2,
      --新补齐的均价字段
      FIRST_VALUE(SS.ACTUAL_QTY) OVER(
        PARTITION BY SS.LV0_CODE,
        SS.LV1_CODE,
        SS.LV2_CODE,
        SS.LV3_CODE,
        SS.LV4_CODE,
        NVL(SS.SPART_CODE, 'SPART'),
        NVL(SS.DIMENSION_CODE, 'DC'),
        NVL(SS.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
        NVL(SS.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
        SS.REGION_CODE,
        SS.REPOFFICE_CODE,
        SS.BG_CODE,
        SS.OVERSEA_FLAG,
        NVL(SS.CODE_ATTRIBUTES, 'CA'),
        NVL(SS.MAIN_FLAG, 'MF'),
        NVL(SS.SOFTWARE_MARK, 'SW'),
        NVL(SS.VIEW_FLAG, 'VF'),
        SS.AVG_AMT_FLAG
        ORDER BY SS.PERIOD_ID
      ) AS ACTUAL_QTY_2,
      --新补齐的均价字段
      SS.AVG_AMT_FLAG,
      SS.APD_FLAG
    FROM (
        SELECT T1.LV0_CODE,
          T1.LV1_CODE,
          T1.LV2_CODE,
          T1.LV3_CODE,
          T1.LV4_CODE,
          T1.LV0_CN_NAME,
          T1.LV1_CN_NAME,
          T1.LV2_CN_NAME,
          T1.LV3_CN_NAME,
          T1.LV4_CN_NAME,
          T1.DIMENSION_CODE,
          T1.DIMENSION_CN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,
          T1.DIMENSION_SUBCATEGORY_CN_NAME,
          T1.DIMENSION_SUB_DETAIL_CODE,
          T1.DIMENSION_SUB_DETAIL_CN_NAME,
          T1.SPART_CODE,
          T1.SPART_CN_NAME,
          T1.REGION_CODE,
          T1.REGION_CN_NAME,
          T1.REPOFFICE_CODE,
          T1.REPOFFICE_CN_NAME,
          T1.BG_CODE,
          T1.BG_CN_NAME,
          T1.OVERSEA_FLAG,
          T1.CODE_ATTRIBUTES,
          T1.MAIN_FLAG,
          T1.VIEW_FLAG,
          T1.PERIOD_YEAR,
          T1.PERIOD_ID,
          T1.RMB_COST_AMT,
          T1.RMB_AVG_AMT,
          T1.ACTUAL_QTY,
          SUM(T1.NULL_FLAG) OVER(
            PARTITION BY T1.LV0_CODE,
            T1.LV1_CODE,
            T1.LV2_CODE,
            T1.LV3_CODE,
            T1.LV4_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CN_NAME,
            T1.LV2_CN_NAME,
            T1.LV3_CN_NAME,
            T1.LV4_CN_NAME,
            NVL(T1.SPART_CODE, 'SPART'),
            NVL(T1.DIMENSION_CODE, 'DC'),
            NVL(T1.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
            NVL(T1.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
            NVL(T1.CODE_ATTRIBUTES, 'CA'),
            NVL(T1.MAIN_FLAG, 'MF'),
            NVL(T1.SOFTWARE_MARK, 'SW'),
            NVL(T1.VIEW_FLAG, 'VF')
            ORDER BY T1.PERIOD_ID
          ) AS AVG_AMT_FLAG,
          --均价标识: 为空不参与累计加1
          T1.APD_FLAG,
          SOFTWARE_MARK
        FROM (
            SELECT T1.LV0_CODE,
              T1.LV1_CODE,
              T1.LV2_CODE,
              T1.LV3_CODE,
              T1.LV4_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CN_NAME,
              T1.LV2_CN_NAME,
              T1.LV3_CN_NAME,
              T1.LV4_CN_NAME,
              T1.DIMENSION_CODE,
              T1.DIMENSION_CN_NAME,
              T1.DIMENSION_SUBCATEGORY_CODE,
              T1.DIMENSION_SUBCATEGORY_CN_NAME,
              T1.DIMENSION_SUB_DETAIL_CODE,
              T1.DIMENSION_SUB_DETAIL_CN_NAME,
              T1.SPART_CODE,
              T1.SPART_CN_NAME,
              T1.REGION_CODE,
              T1.REGION_CN_NAME,
              T1.REPOFFICE_CODE,
              T1.REPOFFICE_CN_NAME,
              T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.OVERSEA_FLAG,
              T1.CODE_ATTRIBUTES,
              T1.SOFTWARE_MARK,
              T1.MAIN_FLAG,
              T1.VIEW_FLAG,
              T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T2.RMB_COST_AMT,
              T2.RMB_AVG_AMT,
              T2.ACTUAL_QTY,
              DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG,
              --空标识, 用于sum开窗累计
              NVL(T2.APPEND_FLAG, 'Y') AS APD_FLAG --补齐标识：Y为补齐，N为原始存在
            FROM CROSS_JOIN_TEMP T1
              LEFT JOIN DM_REPL_YTD_AVG_TEMP T2 ON T1.PERIOD_ID = T2.PERIOD_ID
              AND T1.LV4_CODE = T2.LV4_CODE
              AND NVL(T1.MAIN_FLAG, 'MF') = NVL(T2.MAIN_FLAG, 'MF')
              AND NVL(T1.CODE_ATTRIBUTES, 'CA') = NVL(T2.CODE_ATTRIBUTES, 'CA')
              AND NVL(T1.SOFTWARE_MARK, 'SW') = NVL(T2.SOFTWARE_MARK, 'SW')
              AND NVL(T1.DIMENSION_CODE, 'DC') = NVL(T2.DIMENSION_CODE, 'DC')
              AND NVL(T1.DIMENSION_SUBCATEGORY_CODE, 'DSC') = NVL(T2.DIMENSION_SUBCATEGORY_CODE, 'DSC')
              AND NVL(T1.DIMENSION_SUB_DETAIL_CODE, 'DSDC') = NVL(T2.DIMENSION_SUB_DETAIL_CODE, 'DSDC')
              AND NVL(T1.SPART_CODE, 'SC') = NVL(T2.SPART_CODE, 'SC')
              AND T1.REGION_CODE = T2.REGION_CODE
              AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
              AND T1.BG_CODE = T2.BG_CODE
              AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
              AND T1.VIEW_FLAG = T2.VIEW_FLAG
          ) T1
      ) SS
  )
SELECT T1.LV0_CODE,
  T1.LV1_CODE,
  T1.LV2_CODE,
  T1.LV3_CODE,
  T1.LV4_CODE,
  T1.LV0_CN_NAME,
  T1.LV1_CN_NAME,
  T1.LV2_CN_NAME,
  T1.LV3_CN_NAME,
  T1.LV4_CN_NAME,
  T1.DIMENSION_CODE,
  T1.DIMENSION_CN_NAME,
  T1.DIMENSION_SUBCATEGORY_CODE,
  T1.DIMENSION_SUBCATEGORY_CN_NAME,
  T1.DIMENSION_SUB_DETAIL_CODE,
  T1.DIMENSION_SUB_DETAIL_CN_NAME,
  T1.VIEW_FLAG,
  T1.CODE_ATTRIBUTES,
  T1.MAIN_FLAG,
  T1.SPART_CODE,
  T1.SPART_CN_NAME,
  T1.REGION_CODE,
  T1.REGION_CN_NAME,
  T1.REPOFFICE_CODE,
  T1.REPOFFICE_CN_NAME,
  T1.BG_CODE,
  T1.BG_CN_NAME,
  T1.OVERSEA_FLAG,
  T1.PERIOD_YEAR,
  T1.PERIOD_ID,
  NVL(T1.RMB_AVG_AMT, T2.RMB_AVG_AMT) AS RMB_AVG_AMT,
  T1.ACTUAL_QTY,
  NVL(T1.RMB_COST_AMT, T2.RMB_COST_AMT) AS RMB_COST_AMT,
  T1.APPEND_FLAG,
  T1.SOFTWARE_MARK
FROM (
    --向后补齐均价
    SELECT distinct S.LV0_CODE,
      S.LV1_CODE,
      S.LV2_CODE,
      S.LV3_CODE,
      S.LV4_CODE,
      S.LV0_CN_NAME,
      S.LV1_CN_NAME,
      S.LV2_CN_NAME,
      S.LV3_CN_NAME,
      S.LV4_CN_NAME,
      S.DIMENSION_CODE,
      S.DIMENSION_CN_NAME,
      S.DIMENSION_SUBCATEGORY_CODE,
      S.DIMENSION_SUBCATEGORY_CN_NAME,
      S.DIMENSION_SUB_DETAIL_CODE,
      S.DIMENSION_SUB_DETAIL_CN_NAME,
      S.VIEW_FLAG,
      S.CODE_ATTRIBUTES,
      S.MAIN_FLAG,
      S.SPART_CODE,
      S.SPART_CN_NAME,
      S.REGION_CODE,
      S.REGION_CN_NAME,
      S.REPOFFICE_CODE,
      S.REPOFFICE_CN_NAME,
      S.BG_CODE,
      S.BG_CN_NAME,
      S.OVERSEA_FLAG,
      S.PERIOD_YEAR,
      S.PERIOD_ID,
      NVL(S.RMB_AVG_AMT_2, S.RMB_AVG_AMT_3) AS RMB_AVG_AMT,
      NVL(S.ACTUAL_QTY_2, S.ACTUAL_QTY_3) AS ACTUAL_QTY,
      NVL(S.RMB_COST_AMT_2, S.RMB_COST_AMT_3) AS RMB_COST_AMT,
      S.APD_FLAG AS APPEND_FLAG,
      S.SOFTWARE_MARK
    FROM (
        SELECT T1.LV0_CODE,
          T1.LV1_CODE,
          T1.LV2_CODE,
          T1.LV3_CODE,
          T1.LV4_CODE,
          T1.LV0_CN_NAME,
          T1.LV1_CN_NAME,
          T1.LV2_CN_NAME,
          T1.LV3_CN_NAME,
          T1.LV4_CN_NAME,
          T1.DIMENSION_CODE,
          T1.DIMENSION_CN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,
          T1.DIMENSION_SUBCATEGORY_CN_NAME,
          T1.DIMENSION_SUB_DETAIL_CODE,
          T1.DIMENSION_SUB_DETAIL_CN_NAME,
          T1.SPART_CODE,
          T1.SPART_CN_NAME,
          T1.REGION_CODE,
          T1.REGION_CN_NAME,
          T1.REPOFFICE_CODE,
          T1.REPOFFICE_CN_NAME,
          T1.BG_CODE,
          T1.BG_CN_NAME,
          T1.OVERSEA_FLAG,
          T1.CODE_ATTRIBUTES,
          T1.MAIN_FLAG,
          T1.VIEW_FLAG,
          T1.PERIOD_YEAR,
          T1.PERIOD_ID,
          T1.RMB_COST_AMT_2,
          T2.RMB_COST_AMT_3,
          T1.RMB_AVG_AMT_2,
          T2.RMB_AVG_AMT_3,
          T1.ACTUAL_QTY_2,
          T2.ACTUAL_QTY_3,
          T1.APD_FLAG,
          T1.SOFTWARE_MARK
        FROM FORWARD_FILLER_TEMP T1
          LEFT JOIN (
            SELECT DISTINCT S.LV4_CODE,
              S.DIMENSION_CODE,
              S.DIMENSION_SUBCATEGORY_CODE,
              S.DIMENSION_SUB_DETAIL_CODE,
              S.SPART_CODE,
              S.REGION_CODE,
              S.REPOFFICE_CODE,
              S.BG_CODE,
              S.OVERSEA_FLAG,
              S.VIEW_FLAG,
              S.MAIN_FLAG,
              S.CODE_ATTRIBUTES,
              S.PERIOD_YEAR,
              FIRST_VALUE(S.PERIOD_ID) OVER(
                PARTITION BY S.LV0_CODE,
                S.LV1_CODE,
                S.LV2_CODE,
                S.LV3_CODE,
                S.LV4_CODE,
                NVL(S.SPART_CODE, 'SPART'),
                NVL(S.DIMENSION_CODE, 'DC'),
                NVL(S.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
                NVL(S.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
                S.REGION_CODE,
                S.REPOFFICE_CODE,
                S.BG_CODE,
                S.OVERSEA_FLAG,
                S.SOFTWARE_MARK,
                NVL(S.CODE_ATTRIBUTES, 'CA'),
                NVL(S.MAIN_FLAG, 'MF'),
                NVL(S.VIEW_FLAG, 'VF')
                ORDER BY S.PERIOD_ID ASC
              ) AS PERIOD_ID,
              --有均价的首条会计期
              FIRST_VALUE(S.RMB_AVG_AMT_2) OVER(
                PARTITION BY S.LV0_CODE,
                S.LV1_CODE,
                S.LV2_CODE,
                S.LV3_CODE,
                S.LV4_CODE,
                NVL(S.SPART_CODE, 'SPART'),
                NVL(S.DIMENSION_CODE, 'DC'),
                NVL(S.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
                NVL(S.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
                S.REGION_CODE,
                S.REPOFFICE_CODE,
                S.BG_CODE,
                S.OVERSEA_FLAG,
                S.SOFTWARE_MARK,
                NVL(S.CODE_ATTRIBUTES, 'CA'),
                NVL(S.MAIN_FLAG, 'MF'),
                NVL(S.VIEW_FLAG, 'VF')
                ORDER BY S.PERIOD_ID ASC
              ) AS RMB_AVG_AMT_3,
              --有量的首条补齐量
              FIRST_VALUE(S.RMB_COST_AMT_2) OVER(
                PARTITION BY S.LV0_CODE,
                S.LV1_CODE,
                S.LV2_CODE,
                S.LV3_CODE,
                S.LV4_CODE,
                NVL(S.SPART_CODE, 'SPART'),
                NVL(S.DIMENSION_CODE, 'DC'),
                NVL(S.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
                NVL(S.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
                S.REGION_CODE,
                S.REPOFFICE_CODE,
                S.BG_CODE,
                S.OVERSEA_FLAG,
                S.SOFTWARE_MARK,
                NVL(S.CODE_ATTRIBUTES, 'CA'),
                NVL(S.MAIN_FLAG, 'MF'),
                NVL(S.VIEW_FLAG, 'VF')
                ORDER BY S.PERIOD_ID ASC
              ) AS RMB_COST_AMT_3,
              --有量的首条补齐量										
              FIRST_VALUE(S.ACTUAL_QTY_2) OVER(
                PARTITION BY S.LV0_CODE,
                S.LV1_CODE,
                S.LV2_CODE,
                S.LV3_CODE,
                S.LV4_CODE,
                NVL(S.SPART_CODE, 'SPART'),
                NVL(S.DIMENSION_CODE, 'DC'),
                NVL(S.DIMENSION_SUBCATEGORY_CODE, 'DSC'),
                NVL(S.DIMENSION_SUB_DETAIL_CODE, 'DSDC'),
                S.REGION_CODE,
                S.REPOFFICE_CODE,
                S.BG_CODE,
                S.OVERSEA_FLAG,
                S.SOFTWARE_MARK,
                NVL(S.CODE_ATTRIBUTES, 'CA'),
                NVL(S.MAIN_FLAG, 'MF'),
                NVL(S.VIEW_FLAG, 'VF')
                ORDER BY S.PERIOD_ID ASC
              ) AS ACTUAL_QTY_3,
              --有均价的首条补齐均价
              SOFTWARE_MARK
            FROM FORWARD_FILLER_TEMP S
            WHERE S.AVG_AMT_FLAG > 0
          ) T2 ON T1.LV4_CODE = T2.LV4_CODE
          AND NVL(T1.MAIN_FLAG, 'MF') = NVL(T2.MAIN_FLAG, 'MF')
          AND NVL(T1.CODE_ATTRIBUTES, 'CA') = NVL(T2.CODE_ATTRIBUTES, 'CA')
          AND NVL(T1.SOFTWARE_MARK, 'SW') = NVL(T2.SOFTWARE_MARK, 'SW')
          AND NVL(T1.DIMENSION_CODE, 'DC') = NVL(T2.DIMENSION_CODE, 'DC')
          AND NVL(T1.DIMENSION_SUBCATEGORY_CODE, 'DSC') = NVL(T2.DIMENSION_SUBCATEGORY_CODE, 'DSC')
          AND NVL(T1.DIMENSION_SUB_DETAIL_CODE, 'DSDC') = NVL(T2.DIMENSION_SUB_DETAIL_CODE, 'DSDC')
          AND NVL(T1.SPART_CODE, 'SC') = NVL(T2.SPART_CODE, 'SC')
          AND T1.REGION_CODE = T2.REGION_CODE
          AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
          AND T1.BG_CODE = T2.BG_CODE
          AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
          AND T1.VIEW_FLAG = T2.VIEW_FLAG
          AND T1.PERIOD_ID < T2.PERIOD_ID
      ) S
  ) T1
  LEFT JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_ANNL_AVG_T T2 ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
  AND T1.LV0_CODE = T2.LV0_PROD_LIST_CODE
  AND T1.LV1_CODE = T2.LV1_PROD_LIST_CODE
  AND T1.LV2_CODE = T2.LV2_PROD_LIST_CODE
  AND T1.LV3_CODE = T2.LV3_PROD_LIST_CODE
  AND T1.LV4_CODE = T2.LV4_PROD_LIST_CODE
  AND NVL(T1.MAIN_FLAG, 'MF') = NVL(T2.MAIN_FLAG, 'MF')
  AND NVL(T1.CODE_ATTRIBUTES, 'CA') = NVL(T2.CODE_ATTRIBUTES, 'CA')
  AND NVL(T1.SOFTWARE_MARK, 'SW') = NVL(T2.SOFTWARE_MARK, 'SW')
  AND NVL(T1.DIMENSION_CODE, 'DC') = NVL(T2.DIMENSION_CODE, 'DC')
  AND NVL(T1.DIMENSION_SUBCATEGORY_CODE, 'DSC') = NVL(T2.DIMENSION_SUBCATEGORY_CODE, 'DSC')
  AND NVL(T1.DIMENSION_SUB_DETAIL_CODE, 'DSDC') = NVL(T2.DIMENSION_SUB_DETAIL_CODE, 'DSDC')
  AND NVL(T1.SPART_CODE, 'SC') = NVL(T2.SPART_CODE, 'SC')
  AND T1.REGION_CODE = T2.REGION_CODE
  AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
  AND T1.BG_CODE = T2.BG_CODE
  AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
  AND T1.VIEW_FLAG = T2.VIEW_FLAG