# 重构函数部署和使用指南

## 文件说明

重构后只有一个SQL文件：
- `function/f_dm_foc_mid_month_item_dms_refactored.sql` - 完整的重构函数

## 部署步骤

### 1. 直接部署
```sql
-- 在PostgreSQL中执行
\i function/f_dm_foc_mid_month_item_dms_refactored.sql
```

### 2. 验证部署
```sql
-- 检查函数是否创建成功
SELECT proname, proargnames 
FROM pg_proc 
WHERE proname = 'f_dm_foc_mid_month_item_dms_refactored';
```

## 使用方式

### 函数调用
```sql
-- 基本调用格式
SELECT fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    产业类型,     -- 'I'(ICT), 'E'(数字能源), 'IAS'(IAS)
    期间ID,       -- BIGINT类型，如202312
    业务口径,     -- 'C'(发货成本), 'R'(收入时点)
    维度类型,     -- 'U'(通用), 'P'(盈利), 'D'(量纲)
    加密密钥      -- VARCHAR类型
);
```

### 调用示例

#### 1. ICT产业 + 量纲颗粒度 + 发货成本
```sql
SELECT fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    'I',          -- ICT产业
    202312,       -- 2023年12月
    'C',          -- 发货成本
    'D',          -- 量纲颗粒度
    'your_key'    -- 加密密钥
);
```

#### 2. 数字能源 + 通用颗粒度 + 收入时点
```sql
SELECT fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    'E',          -- 数字能源
    202312,       -- 2023年12月
    'R',          -- 收入时点
    'U',          -- 通用颗粒度
    'your_key'    -- 加密密钥
);
```

#### 3. IAS产业 + 盈利颗粒度 + 发货成本
```sql
SELECT fin_dm_opt_foi.f_dm_foc_mid_month_item_dms_refactored(
    'IAS',        -- IAS产业
    202312,       -- 2023年12月
    'C',          -- 发货成本
    'P',          -- 盈利颗粒度
    'your_key'    -- 加密密钥
);
```

## 参数说明

### 产业类型 (f_industry_flag)
- `'I'` - ICT产业
- `'E'` - 数字能源产业
- `'IAS'` - IAS产业

### 业务口径 (f_caliber_flag)
- `'C'` - 发货成本时点
- `'R'` - 收入确认时点

### 维度类型 (f_dimension_type)
- `'U'` - 通用颗粒度（Universal）
- `'P'` - 盈利颗粒度（Profit）
- `'D'` - 量纲颗粒度（Dimension）

### 期间ID (f_period_id)
- 格式：YYYYMM，如202312表示2023年12月

### 加密密钥 (f_keystr)
- 用于数据解密的密钥字符串

## 返回值

函数返回VARCHAR类型：
- `'SUCCESS'` - 执行成功
- `'0'` - 执行失败

同时通过OUT参数 `x_result_status` 返回状态：
- `'1'` - 成功
- `'0'` - 失败

## 目标表说明

根据参数组合，数据会插入到不同的目标表：

### ICT产业目标表
- 通用颗粒度 + 发货成本 → `DM_FOC_MID_MONTH_ITEM_TMP_CU`
- 盈利颗粒度 + 发货成本 → `DM_FOC_MID_MONTH_ITEM_TMP_CP`
- 量纲颗粒度 + 发货成本 → `DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS`
- 通用颗粒度 + 收入时点 → `DM_FOC_MID_MONTH_ITEM_TMP_RU`
- 盈利颗粒度 + 收入时点 → `DM_FOC_MID_MONTH_ITEM_TMP_RP`
- 量纲颗粒度 + 收入时点 → `DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS`

### 数字能源产业目标表
表名前缀为 `DM_FOC_ENERGY_`，其他规则同ICT

### IAS产业目标表
表名前缀为 `DM_FOC_IAS_`，其他规则同ICT

## 日志查看

函数执行过程中会记录详细日志，可通过以下方式查看：
```sql
-- 查看最近的执行日志
SELECT * FROM FIN_DM_OPT_FOI.DM_FOI_CAL_LOG_T 
WHERE SP_NAME = 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_DMS_REFACTORED'
ORDER BY LOG_TIME DESC 
LIMIT 20;
```

## 错误处理

如果函数执行失败，可以通过以下方式排查：

1. **检查参数有效性**
   - 确保产业类型、业务口径、维度类型参数值正确
   - 确保期间ID格式正确

2. **查看错误日志**
   - 检查日志表中的错误信息
   - 关注ERRBUF字段的详细错误描述

3. **检查数据权限**
   - 确保有相关表的读写权限
   - 确保加密密钥正确

## 性能监控

可以通过以下SQL监控函数执行性能：
```sql
-- 查看执行时间统计
SELECT 
    CAL_LOG_DESC,
    LOG_TIME,
    DML_ROW_COUNT
FROM FIN_DM_OPT_FOI.DM_FOI_CAL_LOG_T 
WHERE SP_NAME = 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_DMS_REFACTORED'
AND LOG_TIME >= CURRENT_DATE
ORDER BY LOG_TIME;
```
