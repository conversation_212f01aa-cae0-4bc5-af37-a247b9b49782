-- 调试SQL语法问题的测试脚本

-- 模拟变量值
DO $$
DECLARE
  V_DIMENSION_FIELDS TEXT := 'DIMENSION_CODE, DIMENSION_CN_NAME, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, SPART_CODE, SPART_CN_NAME, DMS_CODE, DMS_CN_NAME';
  V_VERSION BIGINT := 62111;
  V_BASE_PERIOD_ID INT := 202401;
  F_VIEW_FLAG VARCHAR(20) := 'VIEW1';
  F_OVERSEA_FLAG VARCHAR(10) := 'N';
  C_INDEX_TABLE VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T';
  V_SQL TEXT;
BEGIN
  
  -- 测试LEV_INDEX_TEMP的INSERT语句
  V_SQL := 'INSERT INTO LEV_INDEX_TEMP
            SELECT VIEW_FLAG,
                   PROD_RND_TEAM_CODE,
                   PROD_RND_TEAM_CN_NAME,
                   ' || V_DIMENSION_FIELDS || ',
                   PERIOD_YEAR,
                   PERIOD_ID,
                   SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
                   PARENT_CODE,
                   PARENT_CN_NAME,
                   GROUP_CODE,
                   GROUP_CN_NAME,
                   GROUP_LEVEL,
                   COST_INDEX,
                   SCENARIO_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME
              FROM ' || C_INDEX_TABLE || '
             WHERE VERSION_ID = ' || V_VERSION || '
               AND BASE_PERIOD_ID = ' || V_BASE_PERIOD_ID || '
               AND VIEW_FLAG = ''' || F_VIEW_FLAG || '''
               AND OVERSEA_FLAG = ''' || F_OVERSEA_FLAG || '''';

  -- 输出生成的SQL以检查语法
  RAISE NOTICE '生成的SQL语句：%', V_SQL;
  
  -- 检查是否有连续的逗号
  IF POSITION(',,' IN V_SQL) > 0 THEN
    RAISE NOTICE '发现连续逗号问题！';
  END IF;
  
END $$;

-- 测试字段列表
SELECT 'DIMENSION_CODE, DIMENSION_CN_NAME, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, SPART_CODE, SPART_CN_NAME, DMS_CODE, DMS_CN_NAME' AS dimension_fields;

-- 验证完整的SELECT语句结构
/*
预期的完整SQL应该是：
INSERT INTO LEV_INDEX_TEMP
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
       DIMENSION_CODE, DIMENSION_CN_NAME, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, SPART_CODE, SPART_CN_NAME, DMS_CODE, DMS_CN_NAME,
       PERIOD_YEAR,
       PERIOD_ID,
       SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
       PARENT_CODE,
       PARENT_CN_NAME,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       COST_INDEX,
       SCENARIO_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T
WHERE VERSION_ID = 62111
  AND BASE_PERIOD_ID = 202401
  AND VIEW_FLAG = 'VIEW1'
  AND OVERSEA_FLAG = 'N';
*/
