CREATE OR REPLACE FUNCTION "PUBLIC"."f_dm_foc_ict_revenue_item_ship_dtl(character varying)"
  RETURNS "pg_catalog"."varchar" AS $BODY$
DECLARE
    -- 入参
    v_period_id ALIAS FOR $1;

    -- 变量
    v_row_count INT;
    v_start_time TIMESTAMP;
    v_end_time TIMESTAMP;
    v_procedure_name VARCHAR(128) := 'f_dm_foc_ict_revenue_item_ship_dtl';
    v_period_id_length INT;
    v_current_month_start DATE;
    v_current_month_end DATE;
    v_last_month_start DATE;
    v_last_month_end DATE;
    v_last_year_month_start DATE;
    v_last_year_month_end DATE;

BEGIN
    -- 日志开始
    v_start_time := clock_timestamp();
    RAISE NOTICE 'Procedure % started at %', v_procedure_name, v_start_time;

    -- 1. 清空目标表当月数据
    RAISE NOTICE 'Step 1: Deleting data from dm_foc_revenue_item_ship_dtl for period_id %', v_period_id;
    DELETE FROM dm_foc_revenue_item_ship_dtl WHERE period_id = v_period_id;
    GET DIAGNOSTICS v_row_count = ROW_COUNT;
    RAISE NOTICE 'Step 1: Deleted % rows.', v_row_count;

    -- 2. 获取周期信息
    RAISE NOTICE 'Step 2: Getting period information for period_id %', v_period_id;
    SELECT
        date_trunc('month', (v_period_id || '01')::date),
        (date_trunc('month', (v_period_id || '01')::date) + interval '1 month - 1 day')::date,
        (date_trunc('month', (v_period_id || '01')::date) - interval '1 month')::date,
        (date_trunc('month', (v_period_id || '01')::date) - interval '1 day')::date,
        (date_trunc('month', (v_period_id || '01')::date) - interval '1 year')::date,
        (date_trunc('month', (v_period_id || '01')::date) - interval '1 year' + interval '1 month - 1 day')::date
    INTO
        v_current_month_start, v_current_month_end,
        v_last_month_start, v_last_month_end,
        v_last_year_month_start, v_last_year_month_end;

    RAISE NOTICE 'Step 2: Current month: % to %', v_current_month_start, v_current_month_end;
    RAISE NOTICE 'Step 2: Last month: % to %', v_last_month_start, v_last_month_end;
    RAISE NOTICE 'Step 2: Last year same month: % to %', v_last_year_month_start, v_last_year_month_end;

    -- 3. 插入当月出货明细
    RAISE NOTICE 'Step 3: Inserting current month shipment details into dm_foc_revenue_item_ship_dtl';
    INSERT INTO dm_foc_revenue_item_ship_dtl (
        period_id, item_code, item_name, top_item_code, top_item_name,
        ship_qty, ship_amt, currency_code, data_source, etl_time
    )
    SELECT
        v_period_id,
        t1.item_code,
        t2.item_name,
        t2.top_item_code,
        t2.top_item_name,
        sum(t1.ship_qty),
        sum(t1.ship_amt),
        t1.currency_code,
        'ICT',
        current_timestamp
    FROM fdw_gyl_bigdata.t_dw_om_sale_order_dtl t1
    LEFT JOIN dm_foc_dim_item_d t2 ON t1.item_code = t2.item_code
    WHERE t1.ship_date BETWEEN v_current_month_start AND v_current_month_end
      AND t1.org_code = 'ICT'
    GROUP BY
        t1.item_code, t2.item_name, t2.top_item_code, t2.top_item_name, t1.currency_code;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;
    RAISE NOTICE 'Step 3: Inserted % rows.', v_row_count;

    -- 4. 更新上月出货数量
    RAISE NOTICE 'Step 4: Updating last month shipment quantity';
    WITH last_month_ship AS (
        SELECT
            item_code,
            sum(ship_qty) AS last_month_qty
        FROM fdw_gyl_bigdata.t_dw_om_sale_order_dtl
        WHERE ship_date BETWEEN v_last_month_start AND v_last_month_end
          AND org_code = 'ICT'
        GROUP BY item_code
    )
    UPDATE dm_foc_revenue_item_ship_dtl t
    SET last_month_ship_qty = s.last_month_qty
    FROM last_month_ship s
    WHERE t.period_id = v_period_id AND t.item_code = s.item_code;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;
    RAISE NOTICE 'Step 4: Updated % rows.', v_row_count;

    -- 5. 更新去年同期出货数量
    RAISE NOTICE 'Step 5: Updating last year same month shipment quantity';
    WITH last_year_ship AS (
        SELECT
            item_code,
            sum(ship_qty) AS last_year_qty
        FROM fdw_gyl_bigdata.t_dw_om_sale_order_dtl
        WHERE ship_date BETWEEN v_last_year_month_start AND v_last_year_month_end
          AND org_code = 'ICT'
        GROUP BY item_code
    )
    UPDATE dm_foc_revenue_item_ship_dtl t
    SET last_year_ship_qty = s.last_year_qty
    FROM last_year_ship s
    WHERE t.period_id = v_period_id AND t.item_code = s.item_code;

    GET DIAGNOSTICS v_row_count = ROW_COUNT;
    RAISE NOTICE 'Step 5: Updated % rows.', v_row_count;

    -- 日志结束
    v_end_time := clock_timestamp();
    RAISE NOTICE 'Procedure % finished at %', v_procedure_name, v_end_time;
    RAISE NOTICE 'Total execution time: %', v_end_time - v_start_time;

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'An error occurred in %: %', v_procedure_name, SQLERRM;
        RETURN 'FAILURE';
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;