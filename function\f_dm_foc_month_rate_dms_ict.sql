-- Name: F_DM_FOC_MONTH_RATE_DMS_ICT; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOI.F_DM_FOC_MONTH_RATE_DMS_ICT(f_view_flag character varying, f_oversea_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************
最近修改时间: 2025年7月10日
修改人   : 周博孝 z000610297
修改内容 : 1、CTE转临时表：将LEV_INDEX和BASE_YOY两个CTE重构为临时表
          2、将复杂的UNION ALL拆分为两个独立INSERT，便于维护和错误定位
          3、增强错误处理机制，添加临时表清理逻辑，防止资源泄露
          4、添加详细的执行步骤日志，便于问题追踪和性能监控
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间：2024年4月18日15点27分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间： 2023-03-20
创建人  ： 黄心蕊 hwx1187045
修改时间： 2023-08-20
修改人    ：黄心蕊 hwx1187045
修改时间： 2024-03-06
修改人    ：黄心蕊 hwx1187045
修改描述： 202403版本 新增PARENT_CN_NAME
修改时间：2024-03-27
修改人   ：黄心蕊 hwx1187045
修改内容： 20240327 修改版本号取数逻辑
背景描述：月度分析-同环比表数据初始化
参数描述：参数一(F_ITEM_VERSION)：通用版本号
          参数二(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
          参数三(F_DIMENSION_TYPE)：入参值为'U'为通用颗粒度，入参值为'U'则为盈利颗粒度，入参值为'D'则为量纲颗粒度
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_RATE('U'); 通用颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_RATE('P'); 盈利颗粒度一个版本的数据

****************************************************************************************************************************************************************/
DECLARE
  -- ========== 常量配置区域 ==========
  -- 系统常量
  C_SP_NAME                       CONSTANT VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MONTH_RATE_DMS_ICT';
  C_INDEX_TABLE                   CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T';
  C_TARGET_TABLE                  CONSTANT VARCHAR(100) := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_RATE_T';
  C_CREATED_BY                    CONSTANT VARCHAR(10) := '-1';
  C_DEL_FLAG                      CONSTANT VARCHAR(1) := 'N';
  C_STATUS_SUCCESS                CONSTANT VARCHAR(1) := '1';
  C_STATUS_FAIL                   CONSTANT VARCHAR(1) := '0';

  -- 业务常量
  C_RATE_FLAG_YOY                 CONSTANT VARCHAR(10) := 'YOY';  -- 同比标识
  C_RATE_FLAG_POP                 CONSTANT VARCHAR(10) := 'POP';  -- 环比标识
  C_VERSION_DATA_TYPE             CONSTANT VARCHAR(10) := 'ITEM'; -- 版本数据类型

  -- ========== 变量声明区域 ==========
  V_VERSION                       BIGINT; -- 版本号
  V_STEP_NUM                      INT := 0; -- 执行步骤编号
  V_BASE_PERIOD_ID                INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); -- 基期会计期
  V_SQL                           TEXT; -- 执行语句

  -- 维度字段配置（根据F_DIMENSION_TYPE动态设置）
  V_DIMENSION_FIELDS              TEXT; -- 维度相关字段集合
  V_PARTITION_FIELDS              TEXT; -- 分区字段集合
  V_ORDER_FIELDS                  TEXT; -- 排序字段集合
  
BEGIN
  -- ========== 初始化和参数验证 ==========
  X_RESULT_STATUS := C_STATUS_SUCCESS;

  -- 0.记录函数开始执行日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => C_SP_NAME||'开始执行，参数：产业标识=I，维度类型=D，视图标识='||F_VIEW_FLAG||'，海外标识='||F_OVERSEA_FLAG);


  -- 1.获取版本号：优先使用入参，否则取最新版本
  IF F_ITEM_VERSION IS NULL THEN
    -- 从版本信息表获取最新的ITEM类型版本号
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = C_DEL_FLAG
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = C_VERSION_DATA_TYPE
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1;
  ELSE
    -- 使用传入的版本号
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --1.写入日志
  -- 记录版本号获取成功日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '成功获取版本号：'||V_VERSION||'，产业标识：I，维度类型：D',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 2.根据维度类型配置字段集合（消除重复的字段拼接逻辑）
  -- 注意：当前代码显示这是DMS相关的函数，所以维度字段是固定的
  -- 确保字段列表不会导致SQL语法错误
  V_DIMENSION_FIELDS := 'DIMENSION_CODE, DIMENSION_CN_NAME, ' ||
                        'DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, ' ||
                        'DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, ' ||
                        'SPART_CODE, SPART_CN_NAME, ' ||  -- 202401版本量纲新增SPART层级
                        'DMS_CODE, DMS_CN_NAME';

  
  -- 配置分区字段（用于LAG窗口函数）
  V_PARTITION_FIELDS := 'CALIBER_FLAG, VIEW_FLAG, PROD_RND_TEAM_CODE, ' ||
                        'DIMENSION_CODE, DIMENSION_CN_NAME, ' ||
                        'DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, ' ||
                        'DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME, ' ||
                        'SPART_CODE, SPART_CN_NAME, ' ||
                        'DMS_CODE, DMS_CN_NAME, ' ||
                        'CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, ' ||
                        'GROUP_CODE';

  -- 配置排序字段
  V_ORDER_FIELDS := 'PERIOD_ID';

	

  
  -- ========== 数据清理阶段 ==========

  -- 3.删除目标表中相同版本的历史数据
  V_SQL := 'DELETE FROM ' || C_TARGET_TABLE ||
           ' WHERE VERSION_ID = ' || V_VERSION ||
           ' AND VIEW_FLAG = ''' || F_VIEW_FLAG || '''' ||
           ' AND OVERSEA_FLAG = ''' || F_OVERSEA_FLAG || '''';

  EXECUTE IMMEDIATE V_SQL;

  -- 记录数据删除日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '目标表历史数据清理完成，删除条件：VERSION_ID=' || V_VERSION ||
                     ', VIEW_FLAG=' || F_VIEW_FLAG || ', OVERSEA_FLAG=' || F_OVERSEA_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


  DROP TABLE IF EXISTS LEV_INDEX_TEMP;
  DROP TABLE IF EXISTS BASE_YOY_TEMP;
  -- ========== 数据处理阶段：使用临时表替代CTE ==========

  -- 创建临时表结构：LEV_INDEX_TEMP（替代LEV_INDEX CTE）
  -- 该表存储基础指数数据，用于后续的同环比计算
  V_SQL := 'DROP TABLE IF EXISTS LEV_INDEX_TEMP;
            CREATE TEMPORARY TABLE LEV_INDEX_TEMP (
              VIEW_FLAG VARCHAR(2),
              PROD_RND_TEAM_CODE VARCHAR(50),
              PROD_RND_TEAM_CN_NAME VARCHAR(200),
              DIMENSION_CODE VARCHAR(500),
              DIMENSION_CN_NAME VARCHAR(2000),
              DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
              DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
              DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
              DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
              SPART_CODE VARCHAR(50),
              SPART_CN_NAME VARCHAR(200),
              DMS_CODE VARCHAR(50),
              DMS_CN_NAME VARCHAR(200),
              PERIOD_YEAR INT,
              PERIOD_ID INT,
              MONTH_DAY VARCHAR(2),
              PARENT_CODE VARCHAR(50),
              PARENT_CN_NAME VARCHAR(200),
              GROUP_CODE VARCHAR(50),
              GROUP_CN_NAME VARCHAR(1000),
              GROUP_LEVEL VARCHAR(50),
              COST_INDEX NUMERIC,
              SCENARIO_FLAG VARCHAR(10),
              CALIBER_FLAG VARCHAR(10),
              OVERSEA_FLAG VARCHAR(10),
              LV0_PROD_LIST_CODE VARCHAR(50),
              LV0_PROD_LIST_CN_NAME VARCHAR(200)
            ) 
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(GROUP_CODE)';

  EXECUTE IMMEDIATE V_SQL;

  -- 插入数据到LEV_INDEX_TEMP临时表
  V_SQL := 'INSERT INTO LEV_INDEX_TEMP
            SELECT VIEW_FLAG,
                   PROD_RND_TEAM_CODE,
                   PROD_RND_TEAM_CN_NAME,
                   DIMENSION_CODE,
                   DIMENSION_CN_NAME,
                   DIMENSION_SUBCATEGORY_CODE,
                   DIMENSION_SUBCATEGORY_CN_NAME,
                   DIMENSION_SUB_DETAIL_CODE,
                   DIMENSION_SUB_DETAIL_CN_NAME,
                   SPART_CODE,
                   SPART_CN_NAME,
                   DMS_CODE,
                   DMS_CN_NAME,
                   PERIOD_YEAR,
                   PERIOD_ID,
                   SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
                   PARENT_CODE,
                   PARENT_CN_NAME,  -- 202403版本 新增PARENT_CN_NAME
                   GROUP_CODE,
                   GROUP_CN_NAME,
                   GROUP_LEVEL,
                   COST_INDEX,
                   SCENARIO_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME
              FROM ' || C_INDEX_TABLE || '
             WHERE VERSION_ID = ' || V_VERSION || '
               AND BASE_PERIOD_ID = ' || V_BASE_PERIOD_ID || '
               AND VIEW_FLAG = ''' || F_VIEW_FLAG || '''
               AND OVERSEA_FLAG = ''' || F_OVERSEA_FLAG || '''';


  EXECUTE IMMEDIATE V_SQL;

  -- 记录LEV_INDEX_TEMP临时表数据插入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'LEV_INDEX_TEMP临时表创建并插入数据完成，数据条数：' || SQL%ROWCOUNT,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

 -- 

  -- 7.创建临时表结构：BASE_YOY_TEMP（替代BASE_YOY CTE）
  -- 该表使用LAG窗口函数计算同比和环比的基期数据
  V_SQL := 'DROP TABLE IF EXISTS BASE_YOY_TEMP;
            CREATE TEMPORARY TABLE BASE_YOY_TEMP (
              VIEW_FLAG VARCHAR(20),
              PERIOD_YEAR INT,
              PERIOD_ID INT,
              PROD_RND_TEAM_CODE VARCHAR(50),
              PROD_RND_TEAM_CN_NAME VARCHAR(200),
              DIMENSION_CODE VARCHAR(50),
              DIMENSION_CN_NAME VARCHAR(2000),
              DIMENSION_SUBCATEGORY_CODE VARCHAR(50),
              DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
              DIMENSION_SUB_DETAIL_CODE VARCHAR(50),
              DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200),
              SPART_CODE VARCHAR(50),
              SPART_CN_NAME VARCHAR(50),
              DMS_CODE VARCHAR(50),
              DMS_CN_NAME VARCHAR(200),
              GROUP_CODE VARCHAR(50),
              GROUP_CN_NAME VARCHAR(1000),
              GROUP_LEVEL VARCHAR(50),
              COST_INDEX NUMERIC,
              YOY_PERIOD_ID INT,
              YOY_COST_INDEX NUMERIC,
              POP_PERIOD_ID INT,
              POP_COST_INDEX NUMERIC,
              PARENT_CODE VARCHAR(50),
              PARENT_CN_NAME VARCHAR(200),
              SCENARIO_FLAG VARCHAR(10),
              CALIBER_FLAG VARCHAR(10),
              OVERSEA_FLAG VARCHAR(10),
              LV0_PROD_LIST_CODE VARCHAR(50),
              LV0_PROD_LIST_CN_NAME VARCHAR(200)
            ) ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(GROUP_CODE)';

  EXECUTE IMMEDIATE V_SQL;

    -- 对 YOY 分区字段创建索引
  CREATE INDEX idx_lev_index_temp_yoy_partition
  ON LEV_INDEX_TEMP (
      CALIBER_FLAG,
      VIEW_FLAG,
      PROD_RND_TEAM_CODE,
      DIMENSION_CODE,
      DIMENSION_CN_NAME,
      DIMENSION_SUBCATEGORY_CODE,
      DIMENSION_SUBCATEGORY_CN_NAME,
      DIMENSION_SUB_DETAIL_CODE,
      DIMENSION_SUB_DETAIL_CN_NAME,
      SPART_CODE,
      SPART_CN_NAME,
      DMS_CODE,
      DMS_CN_NAME,
      OVERSEA_FLAG,
      LV0_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      GROUP_CODE,
      MONTH_DAY,
      PERIOD_ID
  );

  -- 对 POP 分区字段创建索引
  CREATE INDEX idx_lev_index_temp_pop_partition
  ON LEV_INDEX_TEMP (
      CALIBER_FLAG,
      VIEW_FLAG,
      PROD_RND_TEAM_CODE,
      DIMENSION_CODE,
      DIMENSION_CN_NAME,
      DIMENSION_SUBCATEGORY_CODE,
      DIMENSION_SUBCATEGORY_CN_NAME,
      DIMENSION_SUB_DETAIL_CODE,
      DIMENSION_SUB_DETAIL_CN_NAME,
      SPART_CODE,
      SPART_CN_NAME,
      DMS_CODE,
      DMS_CN_NAME,
      OVERSEA_FLAG,
      LV0_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      GROUP_CODE,
      PERIOD_ID
  );

  -- 插入数据到BASE_YOY_TEMP临时表，使用LAG窗口函数计算同环比基期数据
  
-- 使用 CTE 预先定义分区字段，提高可读性和维护性
  WITH BASE_CTE AS (
      SELECT 
          VIEW_FLAG,
          PERIOD_YEAR,
          PERIOD_ID,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          DIMENSION_CODE,
          DIMENSION_CN_NAME,
          DIMENSION_SUBCATEGORY_CODE,
          DIMENSION_SUBCATEGORY_CN_NAME,
          DIMENSION_SUB_DETAIL_CODE,
          DIMENSION_SUB_DETAIL_CN_NAME,
          SPART_CODE,
          SPART_CN_NAME,
          DMS_CODE,
          DMS_CN_NAME,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          COST_INDEX,
          PARENT_CODE,
          PARENT_CN_NAME,
          SCENARIO_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME,
          MONTH_DAY,
          -- 定义 YOY 分区字段
          YOY_PARTITION_KEY = (
              CALIBER_FLAG || '|' ||
              VIEW_FLAG || '|' ||
              PROD_RND_TEAM_CODE || '|' ||
              DIMENSION_CODE || '|' ||
              DIMENSION_CN_NAME || '|' ||
              DIMENSION_SUBCATEGORY_CODE || '|' ||
              DIMENSION_SUBCATEGORY_CN_NAME || '|' ||
              DIMENSION_SUB_DETAIL_CODE || '|' ||
              DIMENSION_SUB_DETAIL_CN_NAME || '|' ||
              SPART_CODE || '|' ||
              SPART_CN_NAME || '|' ||
              DMS_CODE || '|' ||
              DMS_CN_NAME || '|' ||
              OVERSEA_FLAG || '|' ||
              LV0_PROD_LIST_CODE || '|' ||
              LV0_PROD_LIST_CN_NAME || '|' ||
              GROUP_CODE || '|' ||
              MONTH_DAY
          ),
          -- 定义 POP 分区字段
          POP_PARTITION_KEY = (
              CALIBER_FLAG || '|' ||
              VIEW_FLAG || '|' ||
              PROD_RND_TEAM_CODE || '|' ||
              DIMENSION_CODE || '|' ||
              DIMENSION_CN_NAME || '|' ||
              DIMENSION_SUBCATEGORY_CODE || '|' ||
              DIMENSION_SUBCATEGORY_CN_NAME || '|' ||
              DIMENSION_SUB_DETAIL_CODE || '|' ||
              DIMENSION_SUB_DETAIL_CN_NAME || '|' ||
              SPART_CODE || '|' ||
              SPART_CN_NAME || '|' ||
              DMS_CODE || '|' ||
              DMS_CN_NAME || '|' ||
              OVERSEA_FLAG || '|' ||
              LV0_PROD_LIST_CODE || '|' ||
              LV0_PROD_LIST_CN_NAME || '|' ||
              GROUP_CODE
          )
      FROM LEV_INDEX_TEMP
  )
  INSERT INTO BASE_YOY_TEMP
  SELECT 
      VIEW_FLAG,
      PERIOD_YEAR,
      PERIOD_ID,
      PROD_RND_TEAM_CODE,
      PROD_RND_TEAM_CN_NAME,
      DIMENSION_CODE,
      DIMENSION_CN_NAME,
      DIMENSION_SUBCATEGORY_CODE,
      DIMENSION_SUBCATEGORY_CN_NAME,
      DIMENSION_SUB_DETAIL_CODE,
      DIMENSION_SUB_DETAIL_CN_NAME,
      SPART_CODE,
      SPART_CN_NAME,
      DMS_CODE,
      DMS_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      -- 同比 (YOY)
      LAG(PERIOD_ID) OVER(PARTITION BY YOY_PARTITION_KEY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
      LAG(COST_INDEX) OVER(PARTITION BY YOY_PARTITION_KEY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
      -- 环比 (POP)
      LAG(PERIOD_ID) OVER(PARTITION BY POP_PARTITION_KEY ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
      LAG(COST_INDEX) OVER(PARTITION BY POP_PARTITION_KEY ORDER BY PERIOD_ID) AS POP_COST_INDEX,
      PARENT_CODE,
      PARENT_CN_NAME,
      SCENARIO_FLAG,
      CALIBER_FLAG,
      OVERSEA_FLAG,
      LV0_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME
  FROM BASE_CTE;
  -- EXECUTE IMMEDIATE V_SQL;

  -- 记录BASE_YOY_TEMP临时表数据插入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'BASE_YOY_TEMP临时表创建并插入数据完成，包含LAG窗口函数计算的同环比基期数据，数据条数：' || SQL%ROWCOUNT,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 调试：记录生成的SQL语句
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'DEBUG');


  -- ========== 数据插入阶段：分别插入同比和环比数据 ==========

  -- 10.插入同比(YOY)数据到目标表
  V_SQL := 'INSERT INTO ' || C_TARGET_TABLE || '
            (VERSION_ID, PERIOD_YEAR, PERIOD_ID, BASE_PERIOD_ID,
             PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
             DIMENSION_CODE, DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
             SPART_CODE, SPART_CN_NAME,
             DMS_CODE, DMS_CN_NAME,
             GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL,
             RATE, RATE_FLAG, PARENT_CODE, PARENT_CN_NAME,
             CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE,
             DEL_FLAG, VIEW_FLAG, SCENARIO_FLAG, CALIBER_FLAG,
             OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
            SELECT ' || V_VERSION || ' AS VERSION_ID,
                   PERIOD_YEAR,
                   PERIOD_ID,
                   ' || V_BASE_PERIOD_ID || ' AS BASE_PERIOD_ID,
                   PROD_RND_TEAM_CODE,
                   PROD_RND_TEAM_CN_NAME,
                   DIMENSION_CODE,
                   DIMENSION_CN_NAME,
                   DIMENSION_SUBCATEGORY_CODE,
                   DIMENSION_SUBCATEGORY_CN_NAME,
                   DIMENSION_SUB_DETAIL_CODE,
                   DIMENSION_SUB_DETAIL_CN_NAME,
                   SPART_CODE,
                   SPART_CN_NAME,
                   DMS_CODE,
                   DMS_CN_NAME,
                   GROUP_CODE,
                   GROUP_CN_NAME,
                   GROUP_LEVEL,
                   -- 同比增长率计算：(当期指数 / 上年同期指数) - 1
                   ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
                   ''' || C_RATE_FLAG_YOY || ''' AS RATE_FLAG,
                   PARENT_CODE,
                   PARENT_CN_NAME, -- 202403版本 新增PARENT_CN_NAME
                   ''' || C_CREATED_BY || ''' AS CREATED_BY,
                   CURRENT_TIMESTAMP AS CREATION_DATE,
                   ''' || C_CREATED_BY || ''' AS LAST_UPDATED_BY,
                   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                   ''' || C_DEL_FLAG || ''' AS DEL_FLAG,
                   VIEW_FLAG,
                   SCENARIO_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME
              FROM BASE_YOY_TEMP
             WHERE YOY_COST_INDEX IS NOT NULL';

  EXECUTE IMMEDIATE V_SQL;

  -- 记录同比数据插入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '同比(YOY)数据插入完成，插入条数：' || SQL%ROWCOUNT,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 11.插入环比(POP)数据到目标表
  V_SQL := 'INSERT INTO ' || C_TARGET_TABLE || '
            (VERSION_ID, PERIOD_YEAR, PERIOD_ID, BASE_PERIOD_ID,
             PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
             DIMENSION_CODE, DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
             SPART_CODE, SPART_CN_NAME,
             DMS_CODE, DMS_CN_NAME,
             GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL,
             RATE, RATE_FLAG, PARENT_CODE, PARENT_CN_NAME,
             CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE,
             DEL_FLAG, VIEW_FLAG, SCENARIO_FLAG, CALIBER_FLAG,
             OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
            SELECT ' || V_VERSION || ' AS VERSION_ID,
                   PERIOD_YEAR,
                   PERIOD_ID,
                   ' || V_BASE_PERIOD_ID || ' AS BASE_PERIOD_ID,
                   PROD_RND_TEAM_CODE,
                   PROD_RND_TEAM_CN_NAME,
                   DIMENSION_CODE,
                   DIMENSION_CN_NAME,
                   DIMENSION_SUBCATEGORY_CODE,
                   DIMENSION_SUBCATEGORY_CN_NAME,
                   DIMENSION_SUB_DETAIL_CODE,
                   DIMENSION_SUB_DETAIL_CN_NAME,
                   SPART_CODE,
                   SPART_CN_NAME,
                   DMS_CODE,
                   DMS_CN_NAME,
                   GROUP_CODE,
                   GROUP_CN_NAME,
                   GROUP_LEVEL,
                   -- 环比增长率计算：(当期指数 / 上期指数) - 1
                   ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
                   ''' || C_RATE_FLAG_POP || ''' AS RATE_FLAG,
                   PARENT_CODE,
                   PARENT_CN_NAME, -- 202403版本 新增PARENT_CN_NAME
                   ''' || C_CREATED_BY || ''' AS CREATED_BY,
                   CURRENT_TIMESTAMP AS CREATION_DATE,
                   ''' || C_CREATED_BY || ''' AS LAST_UPDATED_BY,
                   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                   ''' || C_DEL_FLAG || ''' AS DEL_FLAG,
                   VIEW_FLAG,
                   SCENARIO_FLAG,
                   CALIBER_FLAG,
                   OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME
              FROM BASE_YOY_TEMP
             WHERE POP_COST_INDEX IS NOT NULL';

  EXECUTE IMMEDIATE V_SQL;

    -- 记录生成的SQL语句
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'DEBUG');

  -- 记录环比数据插入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '环比(POP)数据插入完成，插入条数：' || SQL%ROWCOUNT,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


    --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||C_TARGET_TABLE;

  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||C_TARGET_TABLE||'统计信息完成!');


  -- ========== 清理和完成阶段 ==========
  -- 12.清理临时表（显式删除临时表，确保资源释放）
  V_SQL := 'DROP TABLE IF EXISTS LEV_INDEX_TEMP';
  EXECUTE IMMEDIATE V_SQL;

  V_SQL := 'DROP TABLE IF EXISTS BASE_YOY_TEMP';
  EXECUTE IMMEDIATE V_SQL;

  V_STEP_NUM := V_STEP_NUM + 1;
  -- 记录函数执行完成日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => C_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '函数执行完成，临时表已清理，同环比数据计算成功',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
    -- 异常处理：记录详细错误信息并清理资源
    X_RESULT_STATUS := C_STATUS_FAIL;

    -- 尝试清理可能存在的临时表
    BEGIN
      EXECUTE IMMEDIATE 'DROP TABLE IF EXISTS LEV_INDEX_TEMP';
      EXECUTE IMMEDIATE 'DROP TABLE IF EXISTS BASE_YOY_TEMP';
    EXCEPTION
      WHEN OTHERS THEN
        -- 忽略清理临时表时的错误
        NULL;
    END;

    -- 记录详细的异常信息
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => C_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => C_SP_NAME || '在第' || V_STEP_NUM || '步执行失败。' ||
                       '错误详情：' || SQLSTATE || ':' || SQLERRM ||
                       '。版本号：' || COALESCE(V_VERSION::TEXT, 'NULL') ||
                       '，基期：' || V_BASE_PERIOD_ID,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => SQLSTATE || ':' || SQLERRM
    );

    RETURN 'FAILED';

 END; 

$$
/

